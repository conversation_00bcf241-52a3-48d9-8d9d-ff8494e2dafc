import { OSS_URL_HOST } from "@/lib/constants";

export type ModelSeriesType = {
	name: string;
	description?: string;
	url: string;
	logo: string;
	new?: boolean;
};

export enum ImageModelLogo {
	Flux = `${OSS_URL_HOST}icon/model/flux-light.webp`,
	Imagen = `${OSS_URL_HOST}icon/model/google-color.webp`,
	Ideogram = `${OSS_URL_HOST}icon/model/ideogram-light.webp`,
	Recraft = `${OSS_URL_HOST}icon/model/recraft-light.webp`,
	Seedream = `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	Qwen = `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	HiDream = `${OSS_URL_HOST}icon/model/hidream-color.webp`,
}
export const imageModelSeries: ModelSeriesType[] = [
	{
		name: "<PERSON>lux",
		url: "/models/image/flux",
		logo: ImageModelLogo.Flux,
	},
	{
		name: "Flux Krea",
		url: "/models/image/flux/flux-krea",
		logo: ImageModelLogo.Flux,
		new: true,
	},
	{
		name: "Flux Kontext",
		url: "/models/image/flux/flux-kontext",
		logo: ImageModelLogo.Flux,
	},
	{
		name: "Imagen 4",
		url: "/models/image/imagen",
		logo: ImageModelLogo.Imagen,
	},
	{
		name: "Ideogram",
		url: "/models/image/ideogram",
		logo: ImageModelLogo.Ideogram,
	},
	{
		name: "Recraft",
		url: "/models/image/recraft",
		logo: ImageModelLogo.Recraft,
	},
	{ name: "Seedream 3", url: "/ai-image-generator?model=seedream-3", logo: ImageModelLogo.Seedream },
	{
		name: "HiDream",
		url: "/models/image/hidream",
		logo: ImageModelLogo.HiDream,
	},
];

export type ImageModel = {
	name: string;
	id: string; // for api, db
	model: string; // for image generatiton third platform api
	description?: string;
	modelStyle?: {
		id: string | null;
		name: string;
	}[];
	credits: number;
	logo?: string;
	textToImage: boolean;
	imageToImage: boolean;
	noAspectRatio?: "all" | "image-to-image";
	new?: boolean;
	pro?: boolean;
	imageInputType?: "Image prompt" | "Image style" | "Image editing";
};

export const FLUX_1_SCHNELL: ImageModel = {
	// https://fal.ai/models/fal-ai/flux/schnell
	name: "Flux.1 Fast",
	// id: "flux-schnell",
	id: "flux-fast",
	model: "flux-schnell",
	description: "A lightning-quick model",
	credits: 2,
	logo: ImageModelLogo.Flux,
	textToImage: true,
	imageToImage: false,
};
// export const FLUX_1_FAST: ImageModel = {
// 	name: "Flux.1 Fast",
// 	id: "flux-1-fast",
// 	model: "flux-dev-ultra-fast",
// 	credits: 2,
// 	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
// 	textToImage: true,
// 	imageToImage: true,
// };
export const FLUX_1_DEV: ImageModel = {
	// https://fal.ai/models/fal-ai/flux/dev
	name: "Flux.1 Dev",
	id: "flux-1-dev",
	model: "flux-dev",
	credits: 4,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	imageInputType: "Image prompt",
};
export const FLUX_KREA: ImageModel = {
	// https://fal.ai/models/fal-ai/flux/krea
	name: "Flux Krea",
	id: "flux-krea",
	model: "flux-krea",
	credits: 4,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	new: true,
	imageInputType: "Image prompt",
};
export const FLUX_1_KONTEXT_DEV: ImageModel = {
	// https://fal.ai/models/fal-ai/flux-kontext/dev
	name: "Flux Kontext Dev",
	id: "flux-1-kontext-dev",
	model: "flux-1-kontext-dev",
	credits: 4,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: false,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	imageInputType: "Image editing",
};
export const FLUX_1_KONTEXT_PRO: ImageModel = {
	// https://fal.ai/models/fal-ai/flux-pro/kontext
	name: "Flux Kontext Pro",
	id: "flux-1-kontext-pro",
	model: "flux-1-kontext-pro",
	credits: 6,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	pro: true,
	imageInputType: "Image editing",
};
export const FLUX_1_KONTEXT_MAX: ImageModel = {
	name: "Flux Kontext Max",
	id: "flux-1-kontext-max",
	model: "flux-1-kontext-max",
	credits: 12,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	pro: true,
	imageInputType: "Image editing",
};
export const FLUX_1_1_PRO: ImageModel = {
	// https://fal.ai/models/fal-ai/flux-pro/v1.1
	name: "Flux1.1 Pro",
	id: "flux-1.1-pro",
	model: "flux-1.1-pro",
	credits: 6,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	pro: true,
	imageInputType: "Image prompt",
};
export const FLUX_1_1_PRO_ULTRA: ImageModel = {
	name: "Flux1.1 Pro Ultra",
	id: "flux-1.1-pro-ultra",
	model: "flux-1.1-pro-ultra",
	credits: 9,
	logo: `${OSS_URL_HOST}icon/model/flux-light.webp`,
	textToImage: true,
	imageToImage: true,
	pro: true,
	imageInputType: "Image prompt",
};
export const IMAGEN_4_PREVIEW: ImageModel = {
	name: "Imagen 4",
	id: "imagen-4-preview",
	model: "imagen4/preview",
	credits: 6,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
	pro: true,
};
export const IMAGEN_4_PREVIEW_FAST: ImageModel = {
	// https://fal.ai/models/fal-ai/imagen4/preview/fast
	name: "Imagen 4 Fast",
	id: "imagen-4-preview-fast",
	model: "imagen4/preview/fast",
	credits: 3,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const IMAGEN_4_PREVIEW_ULTRA: ImageModel = {
	name: "Imagen 4 Ultra",
	id: "imagen-4-preview-ultra",
	model: "imagen4/preview/ultra",
	credits: 9,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
	pro: true,
};
export const IDEOGRAM_3_TURBO: ImageModel = {
	name: "Ideogram 3 Turbo",
	id: "ideogram-3-turbo",
	model: "ideogram3/turbo",
	modelStyle: [
		{
			id: null,
			name: "None",
		},
		{
			id: "AUTO",
			name: "Auto",
		},
		{
			id: "GENERAL",
			name: "General",
		},
		{
			id: "REALISTIC",
			name: "Realistic",
		},
		{
			id: "DESIGN",
			name: "Design",
		},
	],
	credits: 5,
	logo: `${OSS_URL_HOST}icon/model/ideogram-light.webp`,
	textToImage: true,
	imageToImage: true,
	imageInputType: "Image style",
};
export const IDEOGRAM_3_BALANCED: ImageModel = {
	name: "Ideogram 3",
	id: "ideogram-3-balanced",
	model: "ideogram3/balanced",
	modelStyle: [
		{
			id: null,
			name: "None",
		},
		{
			id: "AUTO",
			name: "Auto",
		},
		{
			id: "GENERAL",
			name: "General",
		},
		{
			id: "REALISTIC",
			name: "Realistic",
		},
		{
			id: "DESIGN",
			name: "Design",
		},
	],
	credits: 9,
	logo: `${OSS_URL_HOST}icon/model/ideogram-light.webp`,
	textToImage: true,
	imageToImage: true,
	pro: true,
	imageInputType: "Image style",
};
export const RECRAFT_3: ImageModel = {
	name: "Recraft 3",
	id: "recraft-3",
	model: "recraft/v3",
	modelStyle: [
		{
			id: "realistic_image",
			name: "Realistic",
		},
		{
			id: "digital_illustration",
			name: "Illustration",
		},
	],
	credits: 6,
	logo: `${OSS_URL_HOST}icon/model/recraft-light.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
	pro: true,
	imageInputType: "Image prompt",
};
export const DREAMINA_3_1: ImageModel = {
	// name: "Dreamina 3.1",
	// id: "dreamina-3.1",
	// model: "dreamina/v3.1",
	name: "Seedream 3.1",
	id: "seedream-3.1",
	model: "seedream/v3.1",
	credits: 5,
	// logo: `${OSS_URL_HOST}icon/model/dreamina-color.webp`,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	textToImage: true,
	imageToImage: false,
	new: true,
	pro: true,
};
export const SEEDREAM_3: ImageModel = {
	name: "Seedream 3",
	id: "seedream-3",
	model: "seedream/v3",
	credits: 5,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const QWEN_IMAGE: ImageModel = {
	name: "Qwen",
	id: "qwen-image",
	model: "qwen-image",
	credits: 3,
	logo: `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	textToImage: true,
	imageToImage: false,
	new: true,
};
export const HIDREAM_I1_DEV: ImageModel = {
	name: "HiDream I1 Dev",
	id: "hidream-i1-dev",
	model: "hidream-i1-dev",
	credits: 5,
	logo: `${OSS_URL_HOST}icon/model/hidream-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const HIDREAM_I1_FULL: ImageModel = {
	name: "HiDream I1 Full",
	id: "hidream-i1-full",
	model: "hidream-i1-full",
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/hidream-color.webp`,
	textToImage: true,
	imageToImage: true,
	pro: true,
	imageInputType: "Image prompt",
};
export const HIDREAM_E1_1: ImageModel = {
	name: "HiDream E1.1",
	id: "hidream-e1-1",
	model: "hidream-e1-1",
	credits: 9,
	logo: `${OSS_URL_HOST}icon/model/hidream-color.webp`,
	textToImage: false,
	imageToImage: true,
	noAspectRatio: "all",
	pro: true,
	imageInputType: "Image editing",
};

export const imageModels: ImageModel[] = [
	// FLUX_1_FAST,
	FLUX_1_SCHNELL,
	FLUX_KREA,
	FLUX_1_DEV,
	FLUX_1_1_PRO,
	FLUX_1_1_PRO_ULTRA,
	FLUX_1_KONTEXT_DEV,
	FLUX_1_KONTEXT_PRO,
	FLUX_1_KONTEXT_MAX,
	IMAGEN_4_PREVIEW,
	IMAGEN_4_PREVIEW_FAST,
	IMAGEN_4_PREVIEW_ULTRA,
	IDEOGRAM_3_BALANCED,
	IDEOGRAM_3_TURBO,
	QWEN_IMAGE,
	DREAMINA_3_1,
	SEEDREAM_3,
	RECRAFT_3,
	HIDREAM_I1_DEV,
	HIDREAM_I1_FULL,
	HIDREAM_E1_1,
];
export const textToImageModels: ImageModel[] = imageModels.filter((imageModel) => imageModel.textToImage);
export const imageToImageModels: ImageModel[] = imageModels.filter((imageModel) => imageModel.imageToImage);

export type ImageWithSeriesModel = {
	name: string;
	id: string; // series id
	logo: string;
	description?: string;
	models: ImageModel[];
	new?: boolean;
};
export const imageWithSeriesModels: ImageWithSeriesModel[] = [
	{
		name: "Imagen",
		id: "imagen",
		logo: ImageModelLogo.Imagen,
		models: [IMAGEN_4_PREVIEW_FAST, IMAGEN_4_PREVIEW, IMAGEN_4_PREVIEW_ULTRA],
	},
	{
		name: "Flux",
		id: "flux",
		logo: ImageModelLogo.Flux,
		description: "Most loved by the AI community",
		models: [FLUX_KREA, FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX, FLUX_1_SCHNELL, FLUX_1_DEV, FLUX_1_1_PRO, FLUX_1_1_PRO_ULTRA],
		new: true,
	},
	{
		name: "Ideogram",
		id: "ideogram",
		logo: ImageModelLogo.Ideogram,
		models: [IDEOGRAM_3_TURBO, IDEOGRAM_3_BALANCED],
	},
	{
		name: "Qwen",
		id: "qwen",
		logo: ImageModelLogo.Qwen,
		models: [QWEN_IMAGE],
		new: true,
	},
	{
		name: "Seedream",
		id: "seedream",
		logo: ImageModelLogo.Seedream,
		models: [DREAMINA_3_1, SEEDREAM_3],
		new: true,
	},
	{
		name: "Recraft",
		id: "recraft",
		logo: ImageModelLogo.Recraft,
		models: [RECRAFT_3],
	},
	// {
	// 	name: "HiDream",
	// 	id: "hidream",
	// 	logo: ImageModelLogo.HiDream,
	// 	models: [HIDREAM_I1_FULL, HIDREAM_E1_1],
	// },
];

export const getImageModel = (id: string): ImageModel => {
	const imageModel = imageModels.find((imageModel) => imageModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("imageModel: ", imageModel);
	}
	if (!imageModel) {
		throw new Error("Image model is not found.");
	}
	return imageModel;
};
export const getInitialImageModel = (id: string | null | undefined): ImageModel => {
	if (!id) return FLUX_KREA;

	return imageModels.find((model) => model.id === id) || FLUX_KREA;
};

export const pageImageModels: ImageModel[] = [
	// FLUX_1_FAST,
	FLUX_1_SCHNELL,
	FLUX_KREA,
	FLUX_1_DEV,
	FLUX_1_1_PRO,
	FLUX_1_1_PRO_ULTRA,
	FLUX_1_KONTEXT_DEV,
	IMAGEN_4_PREVIEW_FAST,
	IDEOGRAM_3_TURBO,
	QWEN_IMAGE,
	DREAMINA_3_1,
	SEEDREAM_3,
	RECRAFT_3,
	HIDREAM_I1_FULL,
	HIDREAM_E1_1,
];
export const getImageModelInPage = (id: string): ImageModel => {
	const imageModel = pageImageModels.find((imageModel) => imageModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("imageModel: ", imageModel);
	}
	if (!imageModel) {
		throw new Error("Image model is not found.");
	}
	return imageModel;
};

// Gemini flash
export const geminiFlashCredits = 2;
export const GEMINI_FLASH: ImageModel = {
	name: "Gemini Flash",
	id: "gemini-flash",
	model: "gemini-flash",
	credits: 2,
	textToImage: false,
	imageToImage: true,
};
