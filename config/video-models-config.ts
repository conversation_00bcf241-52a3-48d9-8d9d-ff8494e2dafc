import { OSS_URL_HOST } from "@/lib/constants";
import { KLING_2_1_STANDARD, LTX_13B_DISTILLED, PIXVERSE_4_5, SEEDANCE_1_LITE } from "@/lib/utils-video-model";

export type ModelSeriesType = {
	name: string;
	description?: string;
	url: string;
	logo: string;
	new?: boolean;
};

export enum VedioModelLogo {
	Veo = `${OSS_URL_HOST}icon/model/deepmind-color.webp`,
	Kling = `${OSS_URL_HOST}icon/model/kling-color.webp`,
	Hailuo = `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
	Pixverse = `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
	Seedance = `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	Wan = `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	LTX = `${OSS_URL_HOST}icon/model/lightricks-light.webp`,
}
export const videoModelSeries: ModelSeriesType[] = [
	{
		name: "Veo",
		url: "/models/video/veo",
		logo: VedioModelLogo.Veo,
	},
	{
		name: "Kling",
		url: `/image-to-video?model=${KLING_2_1_STANDARD.id}`,
		logo: VedioModelLogo.Kling,
	},
	{ name: "Hailuo", url: "/models/video/hailuo", logo: VedioModelLogo.Hailuo },
	{ name: "Pixverse", url: `/ai-video-generator?model=${PIXVERSE_4_5.id}`, logo: VedioModelLogo.Pixverse },
	{ name: "Seedance", url: `/ai-video-generator?model=${SEEDANCE_1_LITE.id}`, logo: VedioModelLogo.Seedance },
	{ name: "Wan", url: "/models/video/wan", logo: VedioModelLogo.Wan, new: true },
	{ name: "LTX Video", url: `/ai-video-generator?model=${LTX_13B_DISTILLED.id}`, logo: VedioModelLogo.LTX },
];
