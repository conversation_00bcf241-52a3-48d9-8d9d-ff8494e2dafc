// eslint.config.js
import { defineConfig } from "eslint/config";

// https://github.com/NickvanDyke/eslint-plugin-react-you-might-not-need-an-effect
import youMightNotNeedAnEffect from "eslint-plugin-react-you-might-not-need-an-effect";

export default defineConfig([
	{
		extends: ["next/core-web-vitals"],
		plugins: {
			"react-you-might-not-need-an-effect": youMightNotNeedAnEffect,
		},
		rules: {
			"@next/next/no-img-element": "off",
			"react-you-might-not-need-an-effect/you-might-not-need-an-effect": "warn",
		},
	},
]);
