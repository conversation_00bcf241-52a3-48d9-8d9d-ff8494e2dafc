import "./globals.css";
import { fontHeading, fontSans } from "@/lib/fonts";
import { Toaster } from "@/components/ui/sonner";
import { CookiesProvider } from "next-client-cookies/server";
import NextTopLoader from "nextjs-toploader";
import type { Metadata } from "next";
import { WEBHOST } from "@/lib/constants";
import { InitializeUser } from "@/components/shared/initialize-user";
import PlanDialog from "@/components/shared/plan-dialog";
import SignInDialog from "@/components/shared/sigin-in-dialog";
import { ThemeProvider } from "@/components/provider/theme-provider";
import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoogle } from "@/components/analytics/analytics-google";

export const metadata: Metadata = {
	metadataBase: new URL(WEBHOST),
	openGraph: {
		// title: `${WEBNAME}`,
		type: "website",
		// url: WEBHOST,
		// description: "",
		// images: `${OSS_URL_HOST}/mkt/og-image.webp`,
	},
	twitter: {
		// site: WEBHOST,
	},
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<link rel="icon" href="/favicon.ico" sizes="any" />
			</head>

			{/* bg-[#F3F4EF] */}
			<body className={`${fontSans.variable} font-sans ${fontHeading.variable} overscroll-none`}>
				{/* <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark"> */}
				<NextTopLoader color="#0061ff" initialPosition={0.3} speed={600} crawlSpeed={200} showSpinner={false} shadow={false} />
				<CookiesProvider>
					{children}
					<Toaster richColors position="top-center" />
					<InitializeUser />
					<SignInDialog />
					<PlanDialog />
					<AnalyticsClarity />
					<AnalyticsGoogle />
				</CookiesProvider>
				{/* </ThemeProvider> */}
			</body>
		</html>
	);
}
