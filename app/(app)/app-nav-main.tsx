"use client";

import { type LucideIcon } from "lucide-react";
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { usePathname } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

type ItemType = {
	title: string;
	url: string;
	url2?: string;
	icon?: LucideIcon;
	isActive?: boolean;
};

export function AppNavMain({ items, label, className }: { items: ItemType[]; label: string; className?: string }) {
	const pathname = usePathname();
	return (
		<>
			<SidebarGroup className={cn("py-0", className)}>
				<SidebarGroupLabel>{label}</SidebarGroupLabel>
				<SidebarMenu className="gap-0">
					{items.map((item, index) => (
						<SidebarMenuItem key={index}>
							<NoPrefetchLink href={item.url}>
								<SidebarMenuButton
									tooltip={item.title}
									className="h-9 cursor-pointer text-sm font-normal text-zinc-300 data-[active=true]:text-indigo-400"
									isActive={pathname === item.url || pathname === item.url2 || pathname.startsWith(item.url)}
								>
									{item.icon && <item.icon className="size-4" />}
									<span className="font-normal">{item.title}</span>
								</SidebarMenuButton>
							</NoPrefetchLink>
						</SidebarMenuItem>
					))}
				</SidebarMenu>
			</SidebarGroup>
		</>
	);
}

export function AppNavMyItems({ items }: { items: ItemType[] }) {
	const pathname = usePathname();
	return (
		<>
			<SidebarGroup className="py-0">
				<Separator />
				<SidebarMenu className="mt-2 gap-0">
					{items.map((item, index) => (
						<SidebarMenuItem key={index}>
							<NoPrefetchLink href={item.url}>
								<SidebarMenuButton
									tooltip={item.title}
									className="h-9 cursor-pointer text-sm font-normal text-zinc-300"
									isActive={pathname === item.url || pathname === item.url2}
								>
									{item.icon && <item.icon className="size-4" />}
									<span className="font-normal">{item.title}</span>
								</SidebarMenuButton>
							</NoPrefetchLink>
						</SidebarMenuItem>
					))}
				</SidebarMenu>
			</SidebarGroup>
		</>
	);
}
