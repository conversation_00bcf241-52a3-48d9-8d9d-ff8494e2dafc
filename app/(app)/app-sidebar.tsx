"use client";

import { ComponentProps } from "react";
import { BookImageIcon, FileImage, FilmIcon, GalleryHorizontalEndIcon, ImageIcon, PaletteIcon, ToolCaseIcon, TypeIcon, VideoIcon } from "lucide-react";
import { Sidebar, SidebarContent, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarRail, useSidebar } from "@/components/ui/sidebar";
import { WEBNAME } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { AppNavMain, AppNavMyItems } from "./app-nav-main";
import { Logo } from "@/components/logo";

const data = {
	navMain: [
		{ title: "Image to Video", url: "/image-to-video", icon: FilmIcon },
		{ title: "Text to Video", url: "/ai-video-generator", icon: VideoIcon },
		{ title: "Video Tools", url: "/video-tools", icon: ToolCaseIcon },
	],
	imageAI: [
		{ title: "Image Generator", url: "/ai-image-generator", icon: TypeIcon, url2: "/image-to-image" },
		// { title: "Image to Image", url: "/image-to-image", icon: ImageIcon },
		{
			title: "Image Editor",
			url: "/image-editor",
			icon: FileImage,
		},
		{ title: "Image Tools", url: "/image-tools", icon: ToolCaseIcon },
		// { title: "Image Generators", url: "/image-generators", icon: BookImageIcon },
		{ title: "Photo Effects", url: "/photo-effects", icon: PaletteIcon },
	],
	my: [{ title: "My Creations", url: "/my-creations", icon: GalleryHorizontalEndIcon }],
};

export function AppSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
	const { isMobile } = useSidebar();
	return (
		<Sidebar collapsible="icon" className="top-(--header-height) h-[calc(100svh-var(--header-height))]!" {...props}>
			{isMobile && (
				<SidebarHeader>
					<SidebarMenu>
						<SidebarMenuItem>
							<SidebarMenuButton size="lg" asChild>
								<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
									<Logo className="size-8 rounded border-zinc-700" />
									<span className="text-base font-medium text-zinc-100">{WEBNAME}</span>
								</NoPrefetchLink>
							</SidebarMenuButton>
						</SidebarMenuItem>
					</SidebarMenu>
				</SidebarHeader>
			)}
			<SidebarContent>
				<AppNavMain items={data.navMain} label="Video AI" className="mt-2" />
				<AppNavMain items={data.imageAI} label="Image AI" />
				<AppNavMyItems items={data.my} />
			</SidebarContent>
			<SidebarRail />
		</Sidebar>
	);
}
