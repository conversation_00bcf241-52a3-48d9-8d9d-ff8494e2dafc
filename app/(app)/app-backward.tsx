import { ChevronLeftIcon } from "lucide-react";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { cn } from "@/lib/utils";

export function AppTitleAndBackward({ title, backUrl }: { title: string; backUrl: string }) {
	return (
		<div className="flex flex-row items-center gap-3 border-b border-zinc-700 px-3 pt-2 pb-2 text-sm text-zinc-100 lg:px-4 lg:pt-3">
			<NoPrefetchLink href={backUrl}>
				<ChevronLeftIcon className="size-5 cursor-pointer shadow hover:text-indigo-500" />
			</NoPrefetchLink>
			<div className={cn("font-[350]", "pointer-events-none underline decoration-2 underline-offset-12")}>{title}</div>
		</div>
	);
}
