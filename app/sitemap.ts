import { MetadataRoute } from "next";
// import { i18nConfig } from "@/i18n-config";
import { WEBHOST } from "@/lib/constants";

export const dynamic = "force-static";

interface SitemapEntry {
	url: string;
	lastModified?: Date;
	changeFrequency?: "monthly" | "daily" | "always" | "hourly" | "weekly" | "yearly" | "never";
	priority?: number;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = WEBHOST;

	const nowDate = new Date();

	const imageModels = ["/flux/flux-krea"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	const images = ["/ai-image-generator", "/image-to-image", "/image-tools", "/image-generators", "/photo-effects", "/image-generators/ai-girl-generator"].map(
		(url) => ({
			url: baseUrl + url,
			lastModified: nowDate,
		}),
	);

	const videoModels = ["/wan", "/hailuo", "/veo"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	const videos = ["/ai-video-generator", "/image-to-video", "/video-tools/video-prompt-generator"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	const normal = ["/pricing", "/changelog", "/news", "/terms-of-use", "/privacy-policy"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	return [{ url: baseUrl, lastModified: nowDate }, ...images, ...videos, ...imageModels, ...videoModels, ...normal] as SitemapEntry[];

	// //legal terms
	// const terms: SitemapEntry[] = [{ url: baseUrl + "/terms-of-use" }, { url: baseUrl + "/privacy-policy" }];

	// //by locale
	// const localePages: () => SitemapEntry[] = () => {
	// 	let site: SitemapEntry[] = [];
	// 	i18nConfig.locales.map((locale) => {
	// 		let baseLocaleUrl = baseUrl;
	// 		if (locale !== i18nConfig.defaultLocale) {
	// 			baseLocaleUrl += "/" + locale;
	// 		}
	// 		site.push(
	// 			...[
	// 				{ url: baseLocaleUrl },
	// 				// { url: baseLocaleUrl + "/changelog" },
	// 				// { url: baseLocaleUrl + "/pricing" }
	// 			],
	// 		);
	// 	});
	// 	return site;
	// };

	// return [...localePages(), ...terms];
}
