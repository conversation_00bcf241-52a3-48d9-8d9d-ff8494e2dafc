"use client";

import { BlogCategorySchemaType } from "@/@types/admin/blog/blog";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { useEffect } from "react";

export function InitializeAdmin({ categories }: { categories: BlogCategorySchemaType[] | null }) {
	const { setCategories } = useBlogCategoryStore();

	useEffect(() => {
		setCategories(categories);
	}, []);

	return <></>;
}
