import { notFound } from "next/navigation";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "./admin-sidebar";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { blogCategorySchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc } from "drizzle-orm";
import { BlogCategorySchemaType } from "@/@types/admin/blog/blog";
import { InitializeAdmin } from "./initialize-admin";

async function getCategories() {
	const db = getDB();
	const categories = (await db.select().from(blogCategorySchema).orderBy(desc(blogCategorySchema.id))) as BlogCategorySchemaType[];
	return categories;
}
export default async function RootLayout({ children }: { children: React.ReactNode }) {
	if (!checkAuthAdmin()) {
		return notFound();
	}
	const categories = await getCategories();

	return (
		<>
			<SidebarProvider>
				<AdminSidebar />
				<SidebarInset>
					<div className="flex h-screen w-full flex-col overflow-hidden">{children}</div>
				</SidebarInset>
			</SidebarProvider>
			<InitializeAdmin categories={categories} />
		</>
	);
}
