"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, CheckCircle2, ChevronLeftIcon, ChevronRightIcon, Eye, EyeOff, Loader, MoreVertical, Plus, RefreshCcw, Search, XCircle } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useRouter } from "nextjs-toploader/app";
import { DashHeader } from "../_components/admin-navigate";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { ofetch } from "ofetch";
import { handleError } from "@/@types/error";
import { Skeleton } from "@/components/ui/skeleton";
import { OSS_URL_HOST } from "@/lib/constants";
import { getMediaResultStatusFormatted, MediaResultStatus } from "@/@types/media/media-type";
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem } from "@/components/ui/pagination";
import { useCopyToClipboard } from "usehooks-ts";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { DialogTitle } from "@radix-ui/react-dialog";
import { Hint } from "@/components/ui/custom/hint";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import Zoom from "react-medium-image-zoom";
import "react-medium-image-zoom/dist/styles.css";

export default function VideosComponent() {
	const router = useRouter();
	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};

	// ======== Display images ========
	const [visibility, setVisibility] = useState<boolean | null>(null);
	// Pagination
	const [page, setPage] = useState<number>(1);
	const [totalPage, setTotalPage] = useState<number>(1);
	const [totalCount, setTotalCount] = useState<number>(0);
	const pageSize = 10; // 每页显示的条目数
	// Fetch images
	const [images, setImages] = useState<any[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const fetchImages = async (currentPage: number, needTotal: boolean = false) => {
		try {
			setIsLoading(true);
			const { status, message, images, totalPage, total } = await ofetch(
				`/api/admin/videos?page=${currentPage}&pageSize=${pageSize}&needTotal=${needTotal}${visibility === null ? "" : `&visibility=${visibility}`}`,
				{
					method: "GET",
				},
			);
			handleError(status, message);
			setImages(images as any[]);
			if (needTotal && totalPage !== undefined) {
				if (totalPage === 0) {
					setTotalPage(1);
				} else {
					setTotalPage(totalPage);
				}
				setTotalCount(total);
			}
		} catch (error: any) {
			console.error("Failed to fetch:", error);
		} finally {
			setIsLoading(false);
		}
	};
	useEffect(() => {
		fetchImages(page, true);
	}, []);

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader current={{ title: "Images" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 pt-4 text-start md:container">
					<div className="flex justify-between">
						<div className="flex flex-row items-center space-x-2">
							<Label className="text-muted-foreground shrink-0 font-normal">Visibility</Label>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										size="default"
										className="w-20 cursor-pointer justify-between px-3 transition-all duration-200 hover:scale-[102%]"
									>
										{visibility === null ? "All" : visibility ? "Public" : "Private"}
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[200px]">
									<p className="px-2 py-1.5 text-sm font-medium">Visibility</p>
									{[null, true, false].map((option, index) => (
										<DropdownMenuItem
											key={index}
											className="cursor-pointer items-center justify-between"
											onClick={() => setVisibility(option)}
										>
											{option === null ? "All" : option ? "Public" : "Private"}
											{visibility === option && <Check className="text-teal-500" />}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
							<div className="flex items-center space-x-2">
								<Button
									variant="secondary"
									size="icon"
									onClick={() => {
										setPage(1);
										fetchImages(1, true);
									}}
									className="cursor-pointer"
								>
									<Search />
								</Button>
							</div>
						</div>
						<div className="flex flex-row items-center gap-1 space-x-2">
							<div className="text-muted-foreground text-sm">Total: {totalCount}</div>
							<SubmitButton
								isSubmitting={isLoading}
								variant="secondary"
								size="icon"
								onClick={() => fetchImages(page, true)}
								className="cursor-pointer"
							>
								<RefreshCcw />
							</SubmitButton>
						</div>
					</div>

					<div className="overflow-x-auto">
						<Table className="w-full min-w-[440px]">
							<TableHeader className="[&_tr]:border-b-0">
								<TableRow className="bg-muted">
									<TableHead className="font-normal">ID</TableHead>
									<TableHead className="font-normal">Video</TableHead>
									<TableHead className="font-normal">Prompt</TableHead>
									<TableHead className="font-normal">Tool</TableHead>
									<TableHead className="font-normal">Visibility</TableHead>
									<TableHead className="font-normal">Status</TableHead>
									<TableHead className="font-normal">Created</TableHead>
									<TableHead className="font-normal"></TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{isLoading ? (
									<>
										{Array.from({ length: 3 }).map((_, index) => (
											<TableRow key={index}>
												<TableCell className="max-w-8 min-w-8">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[120px] min-w-[120px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-full min-w-[260px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[120px] min-w-[120px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[72px] min-w-[72px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[60px] min-w-[60px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[140px] min-w-[140px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
												<TableCell className="w-[52px] min-w-[52px]">
													<Skeleton className="h-8 w-full" />
												</TableCell>
											</TableRow>
										))}
									</>
								) : (
									<>
										{images.map((image, index) => (
											<TableRow key={index}>
												<TableCell className="text-muted-foreground max-w-8 min-w-8 py-4">
													<span className="text-sm">{image.id}</span>
												</TableCell>
												<TableCell className="w-[120px] min-w-[120px] shrink-0 font-medium text-gray-800">
													{(image.mediaPaths || image.mediaOriginUrls) && (
														<Dialog>
															<DialogTrigger>
																<video
																	src={image.mediaPaths ? `${OSS_URL_HOST}${image.mediaPaths}` : image.mediaOriginUrls}
																	muted
																	className="aspect-square h-full w-full cursor-pointer rounded object-contain"
																	onContextMenu={(e) => e.preventDefault()}
																	onDragStart={(e) => e.preventDefault()}
																/>
															</DialogTrigger>
															<DialogContent className="gap-0 border-none p-0">
																<DialogTitle />
																<div className="flex w-full items-center justify-center rounded bg-indigo-100 p-4">
																	<video
																		src={image.mediaPaths ? `${OSS_URL_HOST}${image.mediaPaths}` : image.mediaOriginUrls}
																		muted
																		controls
																		className="h-full w-full rounded object-contain"
																		onContextMenu={(e) => e.preventDefault()}
																		onDragStart={(e) => e.preventDefault()}
																		onMouseEnter={(e) => (e.target as HTMLVideoElement).play()}
																		// onMouseLeave={(e) => (e.target as HTMLVideoElement).pause()}
																	/>
																</div>
															</DialogContent>
														</Dialog>
													)}
												</TableCell>
												<TableCell className="text-muted-foreground w-full min-w-[260px] cursor-pointer">
													<Hint label={image.prompt} className="text-zinc-200">
														<p className="line-clamp-4 text-wrap">{image.prompt}</p>
													</Hint>
												</TableCell>
												<TableCell className="text-muted-foreground w-[120px] min-w-[120px]">
													{image.tool && <p className="line-clamp-4 font-medium text-wrap text-indigo-300">{image.tool}</p>}
													{image.model && <p className="line-clamp-4 text-wrap">{image.model}</p>}
												</TableCell>
												<TableCell className="w-[72px] min-w-[72px]">
													{image.visibility ? (
														<Eye className="size-4 text-blue-500" />
													) : (
														<EyeOff className="text-muted-foreground size-4" />
													)}
												</TableCell>
												<TableCell className="w-[60px] min-w-[60px]">
													<Hint
														label={getMediaResultStatusFormatted(image.status)}
														className="max-w-4xl bg-zinc-200 text-sm break-all whitespace-pre-wrap text-zinc-800 shadow-sm"
													>
														<p className="cursor-pointer text-start text-sm hover:underline">
															{image.status === MediaResultStatus.Completed && <CheckCircle2 className="size-4 text-green-500" />}
															{image.status === MediaResultStatus.Failed && <XCircle className="size-4 text-red-500" />}
															{image.status === MediaResultStatus.InProgress && <Loader className="size-4 text-blue-500" />}
														</p>
													</Hint>
												</TableCell>
												<TableCell className="text-muted-foreground w-[140px] min-w-[140px] text-xs">
													{format(image.createdAt, "MM/dd/yyyy HH:mm:ss")}
												</TableCell>
												<TableCell className="w-[52px] min-w-[52px]">
													<div className="flex flex-row items-center">
														<DropdownMenu modal={false}>
															<DropdownMenuTrigger asChild>
																<Button variant="ghost" size="icon" className="shrink-0 cursor-pointer">
																	<MoreVertical className="h-4 w-4" />
																</Button>
															</DropdownMenuTrigger>
															<DropdownMenuContent align="start">
																<DropdownMenuItem className="cursor-pointer" onClick={onCopy(image.prompt)}>
																	Copy Prompt
																</DropdownMenuItem>
															</DropdownMenuContent>
														</DropdownMenu>
													</div>
												</TableCell>
											</TableRow>
										))}
									</>
								)}
							</TableBody>
						</Table>

						<Pagination className="mt-2 pb-4">
							<PaginationContent>
								<PaginationItem>
									<Button
										variant="ghost"
										onClick={() => {
											setPage(page - 1);
											fetchImages(page - 1);
										}}
										disabled={page == 1}
										className="cursor-pointer gap-1"
									>
										<ChevronLeftIcon className="h-4 w-4" />
										<span>Previous</span>
									</Button>
								</PaginationItem>
								{page > 1 && (
									<Button
										variant="ghost"
										onClick={() => {
											setPage(1);
											fetchImages(1);
										}}
										disabled={page == 1}
										className="cursor-pointer gap-1"
									>
										1
									</Button>
								)}
								{page > 2 && (
									<PaginationItem>
										<PaginationEllipsis />
									</PaginationItem>
								)}
								<PaginationItem>
									<Button variant="outline" disabled className="cursor-pointer">
										{page}
									</Button>
								</PaginationItem>
								{page < totalPage - 1 && (
									<PaginationItem>
										<PaginationEllipsis />
									</PaginationItem>
								)}
								{totalPage > 1 && page < totalPage && (
									<PaginationItem>
										<Button
											variant="outline"
											onClick={() => {
												setPage(totalPage);
												fetchImages(totalPage);
											}}
											disabled={page == totalPage}
											className="cursor-pointer"
										>
											{totalPage}
										</Button>
									</PaginationItem>
								)}
								<PaginationItem>
									<Button
										variant="ghost"
										onClick={() => {
											setPage(page + 1);
											fetchImages(page + 1);
										}}
										disabled={page >= totalPage}
										className="cursor-pointer gap-1"
									>
										<span>Next</span>
										<ChevronRightIcon className="h-4 w-4" />
									</Button>
								</PaginationItem>
							</PaginationContent>
						</Pagination>
					</div>
				</div>
			</div>
		</div>
	);
}
