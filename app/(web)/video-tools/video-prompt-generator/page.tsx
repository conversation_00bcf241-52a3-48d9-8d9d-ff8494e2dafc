import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FAQsComponent from "@/components/landing/faqs";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import VideoPrompt from "./video-prompt.client";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `AI Video Prompt Generator: Create Video Prompts | ${WEBNAME}`,
	description:
		"Turn your vision into effective video prompts, guiding camera direction, style, pacing, and special effects for stunning video results every time.",
	alternates: {
		canonical: "/video-tools/video-prompt-generator",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Video Tools", href: "/video-tools" },
					]}
					current={"Video Prompt Generator"}
				/>
			</div>

			<div className="relative pt-8 pb-10">
				<div className="mx-auto max-w-4xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto">
						<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">AI Video Prompt Generator</h1>
						<div className="text-muted-foreground mx-auto mt-4">
							Craft detailed, tailored video prompts that guide AI to produce videos matching your vision. Customize camera angles, style, pacing,
							and effects for consistent, high-quality results.
						</div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap`)}>
							Generate Video Prompt
						</Link>
					</div> */}
				</div>
			</div>

			<div className="pb-20">
				<VideoPrompt />
			</div>

			<HowToUse
				title="How to Create the Perfect AI Video Prompt"
				steps={[
					{
						title: "1. Describe Your Idea",
						description:
							"Share your vision. Begin by describing the main concept for your video. Choose everything you need—camera direction, style, pacing, special effects, and more. You can even upload an image to inspire your prompt.",
					},
					{
						title: "2. Generate Prompt",
						description:
							"With just one click, let our AI video prompt generator transform your ideas into a clear, detailed prompt designed to get impressive video results.",
					},
					{
						title: "3. Create Video",
						description:
							"Copy your custom AI video prompt and use it to create stunning AI videos directly in Dreampik. Enjoy seamless support for multiple AI video models—and watch your ideas turn into reality.",
					},
				]}
			/>

			<FAQsComponent
				title="Video Prompt Generator Related FAQs"
				faqs={[
					{
						question: "What is an AI video prompt generator?",
						answer: "An AI video prompt generator is a tool designed to help you craft clear, creative instructions—or prompts—for AI video generation tools. It supports both text to video prompt generation (helping you turn ideas into detailed instructions) and image to video prompt generation (transforming static images into dynamic video prompts). This helps guide the AI to create videos that match your vision and needs.",
					},
					{
						question: "Why do I need a video prompt generator?",
						answer: "Writing effective prompts can be challenging, especially if you’re not sure how to describe details like camera direction, style, pacing, or special effects. A video prompt generator takes away this guesswork, making it easier for anyone to get high-quality AI-generated videos that truly reflect your ideas. It gives you control without the frustration of trial and error.",
					},
					{
						question: "What is the best video prompt generator?",
						answer: "Dreampik’s video prompt generator stands out because it allows you to customize every key aspect of your AI-driven videos—including pacing, camera angles, style, and special effects. You can upload an image to generate tailored video prompts, or simply start with a text idea. This gives you flexibility and creativity, making it the top choice for anyone wanting to generate the best results with minimal effort.",
					},
					{
						question: "What is Dreampik?",
						answer: "Dreampik is an advanced platform for generating AI images and videos. It supports a variety of cutting-edge AI image and video models, such as Flux, Ideogram, Google Imagen for images, and Google Veo, Kling, Pixverse, Hailuo for videos, and many more. Whether you’re looking to create unique images or customized videos, Dreampik provides all the tools you need to bring your creative visions to life.",
					},
				]}
			/>
		</main>
	);
}
