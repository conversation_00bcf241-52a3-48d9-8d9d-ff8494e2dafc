"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { CoinsIcon, ChevronDownIcon, TerminalIcon, ClipboardIcon, CheckIcon, LoaderIcon, PlusCircleIcon, XIcon, SparklesIcon } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { fileToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { uploadFile } from "@/lib/file/upload-file";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TO_IMAGE, EVENT_GEN_TEXT_TO_IMAGE } from "@/lib/track-events";
import { IMAGE_SIZE_LIMIT_ } from "@/lib/constants";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button, buttonVariants } from "@/components/ui/button";
import { useCopyToClipboard } from "usehooks-ts";
import { useDropzone } from "react-dropzone";

const styles = ["Minimalist", "Simple", "Detailed", "Descriptive", "Dynamic", "Cinematic", "Documentary", "Animation", "Action", "Experimental"];
const cameraStyles = [
	"None",
	"Steadicam flow",
	"Drone aerials",
	"Handheld urgency",
	"Crane elegance",
	"Dolly precision",
	"VR 360",
	"Multi-angle rig",
	"Static tripod",
	"Gimbal smoothness",
	"Slider motion",
	"Jib sweep",
	"POV immersion",
	"Time-slice array",
	"Macro extreme",
	"Tilt-shift miniature",
	"Snorricam character",
	"Whip pan dynamics",
	"Dutch angle tension",
	"Underwater housing",
	"Periscope lens",
];
const cameraDirections = [
	"None",
	"Zoom in",
	"Zoom out",
	"Pan left",
	"Pan right",
	"Tilt up",
	"Tilt down",
	"Orbital rotation",
	"Push in",
	"Pull out",
	"Track forward",
	"Track backward",
	"Spiral in",
	"Spiral out",
	"Arc movement",
	"Diagonal traverse",
	"Vertical rise",
	"Vertical descent",
];
const pacings = [
	"None",
	"Slow burn",
	"Rhythmic pulse",
	"Frantic energy",
	"Ebb and flow",
	"Hypnotic drift",
	"Time-lapse rush",
	"Stop-motion staccato",
	"Gradual build",
	"Quick cut rhythm",
	"Long take meditation",
	"Jump cut energy",
	"Match cut flow",
	"Cross-dissolve dreamscape",
	"Parallel action",
	"Slow motion impact",
	"Ramping dynamics",
	"Montage tempo",
	"Continuous flow",
	"Episodic breaks",
];
const specialEffects = [
	"None",
	"Practical effects",
	"CGI enhancement",
	"Analog glitches",
	"Light painting",
	"Projection mapping",
	"Nanosecond exposures",
	"Double exposure",
	"Smoke diffusion",
	"Lens flare artistry",
	"Particle systems",
	"Holographic overlay",
	"Chromatic aberration",
	"Digital distortion",
	"Wire removal",
	"Motion capture",
	"Miniature integration",
	"Weather simulation",
	"Color grading",
	"Mixed media composite",
	"Neural style transfer",
];
const promtLengths = ["Short", "Medium", "Long"];

export default function VideoPrompt({ imageToImage }: { imageToImage?: boolean }) {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};
	const [showDemo, setShowDemo] = useState(true);

	const [prompt, setPrompt] = useState<string>("");
	const [style, setStyle] = useState<string>("Simple");
	const [cameraStyle, setCameraStyle] = useState<string>(cameraStyles[0]);
	const [cameraDirection, setCameraDirection] = useState<string>(cameraDirections[0]);
	const [pacing, setPacing] = useState<string>(pacings[0]);
	const [specialEffect, setSpecialEffect] = useState<string>(specialEffects[0]);
	const [promtLength, setPromtLength] = useState<string>(promtLengths[1]);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);

	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			const file = acceptedFiles[0];
			if (!file) return;
			if (file.size > IMAGE_SIZE_LIMIT_) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const [result, setResult] = useState<string>("");

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		setShowDemo(false);

		// sendGTMEvent({
		// 	event: image?.url ? EVENT_GEN_IMAGE_TO_IMAGE : EVENT_GEN_TEXT_TO_IMAGE,
		// 	membership_level: user?.membershipLevel,
		// 	model: model.id,
		// });

		try {
			setResult("");
			setSubmitting(true);

			const { status, message, result } = await ofetch("/api/v1/video/tool/prompt-generator", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: promtpTrim,
					style,
					cameraStyle,
					cameraDirection,
					pacing,
					specialEffect,
					promtLength,
					image: image?.url,
				},
			});
			handleError(status, message);
			if (!userHasPaid) {
				refreshUser();
			}

			if (result) setResult(result);

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate prompt:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	return (
		<div className="container grid w-full grid-cols-1 gap-4 px-6 py-4 sm:grid-cols-2">
			{/* Options Section - Top */}
			<div className="bg-muted w-full rounded-xl p-6">
				<div className="grid grid-cols-2 gap-4">
					{/* Concept - spans full width */}
					<div className="col-span-2 space-y-[6px]">
						{/* <p className="text-secondary-foreground text-sm">Concept</p> */}
						<Textarea
							placeholder="Core concept or thematic input. e.g. A futuristic city at dusk."
							rows={5}
							maxLength={2000}
							className={cn("max-h-24 min-h-24 resize-none border-none bg-white shadow-none [&::-webkit-scrollbar]:hidden")}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-sm">Style</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="">{style}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Style</p>
								{styles.map((styleOption, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											styleOption === style && "bg-accent",
										)}
										onClick={() => {
											setStyle(styleOption);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{styleOption}</span>
										</div>
										{/* {styleOption === style && <CircleCheckIcon className="size-4.5 text-black" />} */}
										{styleOption === style && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Camera Style</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<p className={cn(cameraStyle === "None" && "text-muted-foreground")}>{cameraStyle === "None" ? "Camera Style" : cameraStyle}</p>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Camera Style</p>
								{cameraStyles.map((styleOption, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											styleOption === cameraStyle && "bg-accent",
										)}
										onClick={() => {
											setCameraStyle(styleOption);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{styleOption}</span>
										</div>
										{styleOption === cameraStyle && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Camera Direction</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<p className={cn(cameraDirection === "None" && "text-muted-foreground")}>
									{cameraDirection === "None" ? "Camera Direction" : cameraDirection}
								</p>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Camera Direction</p>
								{cameraDirections.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											option === cameraDirection && "bg-accent",
										)}
										onClick={() => {
											setCameraDirection(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === cameraDirection && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Pacing</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<p className={cn(pacing === "None" && "text-muted-foreground")}>{pacing === "None" ? "Pacing" : pacing}</p>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Pacing</p>
								{pacings.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											option === pacing && "bg-accent",
										)}
										onClick={() => {
											setPacing(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === pacing && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Special Effect</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<p className={cn(specialEffect === "None" && "text-muted-foreground")}>
									{specialEffect === "None" ? "Special Effect" : specialEffect}
								</p>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Special Effect</p>
								{specialEffects.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											option === specialEffect && "bg-accent",
										)}
										onClick={() => {
											setSpecialEffect(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === specialEffect && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						{/* <p className="text-secondary-foreground text-xs">Prompt Length</p> */}
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md bg-white px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0 hover:bg-white",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="">{promtLength}</span>
								</div>
								<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="[&::-webkit-scrollbar-thumb]:bg-input min-w-(--radix-dropdown-menu-trigger-width) border-none [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full">
								<p className="text-muted-foreground p-2 text-xs font-medium">Prompt Length</p>
								{promtLengths.map((option, index) => (
									<DropdownMenuItem
										key={index}
										className={cn(
											"flex cursor-pointer flex-row items-center justify-between gap-2 rounded-md py-2.5",
											option === promtLength && "bg-accent",
										)}
										onClick={() => {
											setPromtLength(option);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{option}</span>
										</div>
										{option === promtLength && (
											<p className="flex h-[18px] w-[18px] items-center justify-center rounded-full bg-black">
												<CheckIcon className="size-3 text-white" strokeWidth={3} />
											</p>
										)}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="col-span-2 overflow-hidden rounded-md bg-white" {...getRootProps()}>
						{image ? (
							<div className="group relative h-[144px] w-full">
								<img
									src={image.base64}
									alt="Model"
									className="h-full w-full object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									onClick={() => {
										setImage(null);
									}}
									className="bg-foreground/90 absolute top-2 right-2 flex h-9 w-9 cursor-pointer items-center justify-center rounded-full p-2 text-white"
								>
									<XIcon className="size-4" />
								</button>
							</div>
						) : (
							<div
								onClick={() => {
									if (uploadingImage) return;
									openDropzone();
								}}
								className="flex h-[144px] cursor-pointer flex-col items-center justify-center px-2 text-center"
							>
								{uploadingImage ? (
									<div className="text-muted-foreground flex flex-col items-center">
										<LoaderIcon className="animate-spin" />
										<p className="text-sm">Uploading image</p>
									</div>
								) : (
									<>
										<div className={cn(buttonVariants({ size: "lg", variant: "secondary" }), "bg-input hover:bg-input/80 h-12 rounded-xl")}>
											<PlusCircleIcon />
											<p>Upload image</p>
										</div>
										<p className="text-muted-foreground text-sm">An image to analyze and incorporate into the video prompt (optional)</p>
									</>
								)}
								<input {...getInputProps()} />
							</div>
						)}
					</div>
				</div>

				{/* Generate Button Section */}
				<div className="mt-4 space-y-1">
					{session && !userHasPaid && (
						<p className="text-muted-foreground flex flex-row items-center gap-1 text-[11px] font-[350]">
							<CoinsIcon className="size-3" />0 credits for{" "}
							<button className="text-primary underline underline-offset-4" onClick={() => setPlanBoxOpen(true)}>
								paid
							</button>{" "}
							users.
						</p>
					)}
					<SubmitButton
						size="lg"
						className="bg-foreground hover:bg-foreground/80 w-full rounded-lg"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !prompt.trim() }}
					>
						<SparklesIcon />
						Generate
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								{userHasPaid ? 0 : 2})
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			{/* Result Section - Bottom */}
			<div className="bg-muted flex items-start justify-center rounded-xl p-6">
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full flex-col justify-center space-y-2">
							<p className="text-muted-foreground text-sm">Sample Prompt</p>
							<div className="bg-card mx-auto flex w-full flex-col gap-2 rounded-md p-4 text-sm">
								<TerminalIcon className="text-muted-foreground size-4" />
								<p className="text-foreground">
									As dusk settles over the futuristic city, buildings of glass and steel gleam with soft, reflected light from the setting
									sun. The camera uses a serene slider motion to zoom in, capturing the city from a distance and slowly focusing on the
									intricate details of its architecture. The calming scene emphasizes the gentle transition from day to night, drawing viewers
									into a world where technology and tranquility coexist harmoniously.
								</p>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full flex-col justify-center space-y-2">
							<p className="text-muted-foreground text-sm">Prompt</p>
							<div className="bg-card mx-auto flex min-h-[160px] w-full flex-col justify-between gap-2 rounded-md p-4 text-sm">
								{/* {submitting && (
									<p className="text-muted-foreground flex h-full w-full flex-col items-center justify-center text-center">
										<LoaderIcon className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)} */}
								{result && (
									<>
										<p className="text-foreground">{result}</p>
										<div className="flex w-full justify-end">
											<Button size="sm" variant="secondary" className="font-normal" onClick={onCopy(result)}>
												<ClipboardIcon /> Copy
											</Button>
										</div>
									</>
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
