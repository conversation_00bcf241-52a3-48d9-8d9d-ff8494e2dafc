import type { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";
import FAQsComponent from "@/components/landing/faqs";
import Plans from "@/components/shared/plans-subscription";

export const metadata: Metadata = {
	title: `Plans & Pricing | ${WEBNAME}`,
	description: `Explore our flexible and affordable pricing plans for ${WEBNAME}`,
	alternates: {
		canonical: "/pricing",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="relative pt-16 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-4 text-center sm:mx-auto lg:mt-8 lg:mr-auto">
						<h1 className="text-4xl font-medium md:text-5xl">Plans & Pricing</h1>
						<div className="text-muted-foreground mx-auto mt-4 text-base text-balance">
							Choose the best plan for your needs.
							{/* New user get {membershipMapping[MembershipID.Free].credits} credits for free.
							<HintPopover label={"Due to high demand, free credits are not available in some countries."}>
								<Info className="text-muted-foreground ml-1 inline h-4 w-4 cursor-pointer" />
							</HintPopover> */}
						</div>
					</div>
				</div>
			</div>
			<div className="container flex justify-center pb-20">
				<div className="w-full">
					<Plans />
				</div>
			</div>

			<FAQsComponent
				faqs={[
					{
						question: "Can I use Dreampik for free?",
						answer: "Yes! Every registered user receives 20 free credits to try out our platform and see what you can create. No payment is needed to get started. Please note, due to high demand, free credits may not be available in some countries.",
					},
					{
						question: "Can I use the images and videos I generate for commercial projects?",
						answer: "With a paid plan, yes—you can use your generated images and videos for commercial purposes. Free plan creations are for personal use only and cannot be used in commercial projects.",
					},
					{
						question: "What payment methods do you accept?",
						answer: "Dreampik supports a range of payment options, including major credit cards like Visa, MasterCard, and American Express, as well as other popular payment methods.",
					},
					{
						question: "How are credits deducted for image or video generation?",
						answer: "Credits are used each time you generate an image or video. The number of credits required depends on things like the tool or model you choose, the length of your video, how many outputs you want, and the final resolution.",
					},
				]}
			/>
		</main>
	);
}
