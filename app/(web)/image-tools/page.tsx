import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { allImageTools } from "@/lib/utils-image-tools";

export const metadata: Metadata = {
	title: `AI Image Tools | ${WEBNAME}`,
	alternates: {
		canonical: "/image-tools",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="flex h-full w-full flex-col px-4 py-4">
				<section>
					<div className="relative pt-16 pb-20">
						<div className="mx-auto max-w-5xl px-6">
							<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
								<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">Discover the Power of AI Image Tools</h1>
								<div className="text-muted-foreground mx-auto mt-4 text-lg"></div>
							</div>
						</div>
					</div>
				</section>

				<div className="container flex flex-col items-center gap-12 px-6">
					<div className="mx-auto grid w-full grid-cols-2 gap-4 pt-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
						{allImageTools.map((imageTool, index) => (
							<NoPrefetchLink
								key={index}
								href={imageTool.pageUrl}
								className="bg-muted hover:bg-muted-foreground/20 flex cursor-pointer flex-col rounded-lg transition-all"
							>
								<div className="overflow-hidden rounded-t-md">
									{imageTool.demoImage && (
										<img src={imageTool.demoImage} alt={imageTool.pageName} className="aspect-video h-full w-full object-cover" />
									)}
								</div>
								<div className="p-3">
									<h3 className="text-sm">{imageTool.pageName}</h3>
								</div>
							</NoPrefetchLink>
						))}
					</div>
				</div>
			</div>
		</main>
	);
}
