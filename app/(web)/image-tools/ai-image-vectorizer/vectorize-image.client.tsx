"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, CoinsIcon, LoaderIcon, PlusCircleIcon, SparklesIcon, XIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromUrl, fileToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { uploadFile } from "@/lib/file/upload-file";
import { Comparison, Comparison<PERSON><PERSON>le, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { IMAGE_SIZE_LIMIT_, OSS_URL_HOST } from "@/lib/constants";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_TOOL } from "@/lib/track-events";
import { ReactSVG } from "react-svg";
import { useDropzone } from "react-dropzone";
import { buttonVariants } from "@/components/ui/button";

export function VectorizeImageClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [showDemo, setShowDemo] = useState(true);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [previewImage, setPreviewImage] = useState<string | null>(null);

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			const file = acceptedFiles[0];
			if (!file) return;
			if (file.size > IMAGE_SIZE_LIMIT_) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				// const file_url = "";
				const base64 = await fileToBase64(file);
				setImage({
					url: file_url,
					base64,
				});
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setShowDemo(false);

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_TOOL,
			membership_level: user?.membershipLevel,
			tool: "vectorize-image",
		});

		try {
			setPreviewImage(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/tool/vectorize-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					image: image?.url,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrl = "https://static.dreampik.art/dev/media/202507/2025072001982826800a7758ae4de4650ec74afd.svg";
			setPreviewImage(resultUrl);
			// if (resultUrl) {
			// 	const base64 = await imageUrlToBase64(resultUrl as string, { noCache: true });
			// 	setPreviewImageBase64(base64);
			// }

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImage(null);
			console.error("Failed to generate svg:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message ?? "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="container grid w-full grid-cols-1 gap-4 px-6 py-4 sm:grid-cols-2">
			<div className="bg-muted w-full rounded-xl p-6">
				<div className="bg-input overflow-hidden rounded-lg" {...getRootProps()}>
					{image ? (
						<div className="group relative h-[260px] w-full md:h-[260px] lg:h-[300px]">
							<img
								src={image.base64}
								alt="Model"
								className="h-full w-full object-cover"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
							/>
							<button
								onClick={() => {
									setImage(null);
								}}
								className="bg-foreground/90 absolute top-2 right-2 flex h-9 w-9 cursor-pointer items-center justify-center rounded-full p-2 text-white"
							>
								<XIcon className="size-4" />
							</button>
						</div>
					) : (
						<div
							onClick={() => {
								if (uploadingImage) return;
								openDropzone();
							}}
							className="flex h-[200px] cursor-pointer items-center justify-center md:h-[260px] lg:h-[300px]"
						>
							{uploadingImage ? (
								<div className="text-muted-foreground flex flex-col items-center">
									<LoaderIcon className="animate-spin" />
									<p className="text-sm">Uploading image</p>
								</div>
							) : (
								<div className={cn(buttonVariants({ size: "lg" }), "bg-action hover:bg-action/80 h-14 rounded-xl")}>
									<PlusCircleIcon />
									<p>Upload image</p>
								</div>
							)}
							<input {...getInputProps()} />
						</div>
					)}
				</div>

				<div className="mt-4">
					<SubmitButton
						size="lg"
						className="bg-foreground hover:bg-foreground/80 w-full rounded-lg"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !image }}
					>
						<SparklesIcon />
						Vectorize
						{session && (
							<p className="text-secondary flex flex-row items-center gap-0.5 px-2 py-0.5 text-[11px]">
								(<CoinsIcon className="size-3" />
								3)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>
			<div className="bg-muted flex items-start justify-center rounded-xl p-6">
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full max-w-2xl flex-col justify-center space-y-2">
							<div className="mx-auto flex w-full max-w-3xl overflow-hidden rounded-lg">
								<Comparison className="aspect-[3/2] max-w-3xl">
									<ComparisonItem position="right" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-before.webp`}
												alt="Image Upscaler Sample Before"
												className="h-full w-full object-contain"
											/>
											<div
												className="bg-secondary-foreground/60 absolute top-2 left-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'Image'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonItem position="left" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-vectorizer/vectorize-sample-after.svg`}
												alt="Image Upscaler Sample After"
												className="h-full w-full object-contain"
											/>
											<div
												className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'SVG'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonHandle />
								</Comparison>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
							<div
								className={cn(
									"group relative mx-auto flex w-full max-w-3xl justify-center rounded-lg",
									(previewImage || submitting) && "aspect-[3/2]",
								)}
							>
								{previewImage && (
									<>
										<ReactSVG
											src={previewImage}
											className=""
											wrapper="span"
											beforeInjection={(svg) => {
												svg.removeAttribute("width");
												svg.removeAttribute("height");
												svg.setAttribute("class", "w-full h-full");
												svg.setAttribute("preserveAspectRatio", "xMidYMid meet");
											}}
										/>
										<div
											className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white shadow backdrop-blur-xs after:content-(--content)"
											style={{ "--content": "'SVG'" } as React.CSSProperties}
										></div>
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											<Hint label="Download image">
												<div className="relative">
													<SubmitButton
														isSubmitting={downloadingImage}
														disabled={!previewImage}
														size="icon"
														onClick={async () => {
															try {
																setDownloadingImage(true);
																await downloadImageFromUrl(previewImage);
															} catch (error) {
																console.error("Failed to download image:", error);
															} finally {
																setDownloadingImage(false);
															}
														}}
														className="bg-foreground hover:bg-foreground/80"
													>
														<Download />
													</SubmitButton>
												</div>
											</Hint>
										</div>
									</>
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
