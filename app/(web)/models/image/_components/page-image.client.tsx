"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { SparklesIcon, Download, Trash, Loader2, ImagePlus, ChevronDown, CoinsIcon } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { uploadFile } from "@/lib/file/upload-file";
import { FLUX_1_SCHNELL, ImageModel, pageImageModels } from "@/lib/utils-image-model";
import { Spinner } from "@/components/ui/kibo-ui/spinner";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_PAGE } from "@/lib/track-events";

const genTypes = [
	{
		label: "Text to Image",
		value: "text-to-image",
	},
	{
		label: "Image to Image",
		value: "image-to-image",
	},
];

export default function PageImageClient({
	defaultModelId = FLUX_1_SCHNELL.id,
	defaultGenType = "text-to-image",
}: {
	defaultModelId?: string;
	defaultGenType?: string;
}) {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [genType, setGenType] = useState(genTypes.find((type) => type.value === defaultGenType) ?? genTypes[0]);
	const [model, setModel] = useState<ImageModel>(pageImageModels.find((model) => model.id === defaultModelId) ?? pageImageModels[0]);
	const [prompt, setPrompt] = useState("");

	const [image, setImage] = useState<string | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);
	// const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(
	// 	"https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// );
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			setImage(file_url);
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_PAGE,
			membership_level: user?.membershipLevel,
			model: model.id,
		});

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/page-image", {
				method: "POST",
				body: {
					model: model.id,
					prompt: promtpTrim,
					image: image,
				},
			});
			handleError(status, message);
			void refreshUser();

			if (resultUrls && resultUrls.length > 0) {
				const base64 = await imageUrlToBase64(resultUrls[0], { noCache: true });
				setPreviewImageBase64(base64);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="bg-muted w-full max-w-2xl rounded-xl">
				<div className="flex flex-row items-center justify-center gap-3 px-4 pt-3 pb-2 text-sm">
					{genTypes.map((type, index) => (
						<button
							key={index}
							className={cn(
								"cursor-pointer font-medium",
								type === genType ? "text-action underline decoration-2 underline-offset-8" : "text-muted-foreground hover:text-action",
							)}
							onClick={() => {
								if (type.value === defaultGenType) {
									setModel(pageImageModels.find((m) => m.id === defaultModelId)!);
								} else {
									if (type.value === "image-to-image" && !model.imageToImage) {
										setModel(pageImageModels.find((m) => m.imageToImage)!);
									}
									if (type.value === "text-to-image" && !model.textToImage) {
										setModel(pageImageModels.find((m) => m.imageToImage)!);
									}
								}
								setGenType(type);
							}}
						>
							{type.label}
						</button>
					))}
				</div>

				<div className="space-y-3 px-4 py-3">
					{genType.value === "image-to-image" && (
						<div className="space-y-[6px]">
							<p className="text-xs">Image</p>
							{image ? (
								<div className="group relative h-[144px] w-full rounded-lg border">
									<img
										src={image}
										alt="Model"
										className="h-[142px] w-full rounded-lg object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
									/>
									<div className="absolute inset-0 hidden items-center justify-center rounded-lg group-hover:flex">
										<button
											onClick={() => setImage(null)}
											className="cursor-pointer rounded-full p-2 text-zinc-300/80 transition-colors hover:text-white"
										>
											<Trash className="size-4" />
										</button>
									</div>
								</div>
							) : (
								<div
									onClick={() => {
										if (!session) {
											setSignInBoxOpen(true);
										}
									}}
								>
									<Dropzone
										multiple={false}
										maxFiles={1}
										noClick={!session}
										onDragEnter={() => {
											if (!session) {
												setSignInBoxOpen(true);
												return;
											}
										}}
										onDrop={(files) => handleLocalFileDrop(files)}
										accept={{
											"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
										}}
										onError={console.error}
										className="hover:border-action/50 h-[144px] w-full cursor-pointer border-none p-0 shadow-none hover:border-2 hover:border-dashed hover:bg-white"
									>
										<DropzoneEmptyState>
											<>
												{uploadingImage ? (
													<Loader2 className="text-muted-foreground animate-spin" />
												) : (
													<div className="text-muted-foreground hover:text-muted-foreground flex h-10 flex-col items-center gap-2 font-normal">
														<ImagePlus />
														<p>Click to upload an image</p>
													</div>
												)}
												{/* <p className="w-full text-sm font-normal text-muted-foreground">Or drop an image</p> */}
											</>
										</DropzoneEmptyState>
									</Dropzone>
								</div>
							)}
						</div>
					)}

					<div className="space-y-[6px]">
						<p className="text-xs">Prompt</p>
						<Textarea
							placeholder="Describe your image...."
							rows={5}
							maxLength={2000}
							className={cn(
								"bore h-32 resize-none border-none bg-white shadow-none focus-visible:shadow-none",
								"[&::-webkit-scrollbar-thumb]:bg-muted-foreground [&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>
				</div>

				<div className="mb-2 flex flex-wrap items-center justify-between gap-1 px-4">
					<div className="flex items-center gap-1">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="outline" size="sm" className="cursor-pointer justify-between border font-normal">
									{/* <img src={`${OSS_URL_HOST}icon/model/flux-dark.webp`} className="size-4" alt="" /> */}
									<img src={model.logo} className="size-4" alt="" />
									{model?.name}
									<ChevronDown className="text-muted-foreground" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-[180px]">
								{pageImageModels.map((modelOption, index) => {
									if (genType.value === "image-to-image" && !modelOption.imageToImage) {
										return null;
									}
									if (genType.value === "text-to-image" && !modelOption.textToImage) {
										return null;
									}
									return (
										<DropdownMenuItem key={index} className="cursor-pointer items-center" onClick={() => setModel(modelOption)}>
											{/* <img src={`${OSS_URL_HOST}icon/model/flux-dark.webp`} className="size-4" alt="" /> */}
											<img src={modelOption.logo} className="size-4" alt="" />
											{modelOption.name}
										</DropdownMenuItem>
									);
								})}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0 || (genType.value === "image-to-image" && !image)}
						className="bg-foreground hover:bg-foreground/80"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>
						{session && (
							<p className="flex flex-row items-center gap-0.5 text-xs font-normal">
								(<span>{model.credits}</span>
								<CoinsIcon className="size-3" />)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			<div className="w-full">
				{submitting ? (
					<div className="bg-muted mx-auto flex aspect-video w-full max-w-2xl shrink-0 rounded-lg">
						<div className="text-secondary-foreground mx-auto flex flex-col items-center justify-center gap-2 px-4 text-center">
							<Spinner variant="ellipsis" className="text-action mx-auto" />
							<span className="text-sm tabular-nums">{seconds}s</span>
						</div>
					</div>
				) : (
					previewImageBase64 && (
						<div className="group bg-muted relative mx-auto flex aspect-video w-full max-w-2xl shrink-0 rounded-lg">
							{/* <img
								src={previewImageBase64}
								alt="Generated image"
								className="h-full w-full rounded-lg object-contain"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
							/> */}
							<div
								className="h-full w-full rounded-lg bg-contain bg-center bg-no-repeat"
								style={{ backgroundImage: `url(${previewImageBase64})` }}
								role="img"
								aria-label="Generated image"
							></div>
							<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
								{previewImageBase64 && (
									<Hint label="Download image" sideOffset={10}>
										<div className="relative">
											<SubmitButton
												className="bg-foreground hover:bg-foreground/80"
												isSubmitting={downloading}
												disabled={!previewImageBase64}
												size="icon"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromBase64(previewImageBase64);
														} else {
															await downloadImageFromBase64WithWatermark(previewImageBase64);
														}
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					)
				)}
			</div>
		</div>
	);
}
