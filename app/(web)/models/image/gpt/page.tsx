import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { House } from "lucide-react";
import GPTImageClient from "./gpt-image.client";

export const metadata: Metadata = {
	title: `GPT Image 1: Try GPT Image 1 for Free | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/gpt",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="px-6 pt-2">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/", icon: House },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
					]}
					current={"GPT Image 1"}
				/>
			</div>

			<section>
				<div className="relative pt-16 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 text-center sm:mx-auto lg:mr-auto">
							<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Try GPT Image 1</h1>
							<div className="text-muted-foreground mx-auto mt-4"></div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<GPTImageClient />
			</div>
		</main>
	);
}
