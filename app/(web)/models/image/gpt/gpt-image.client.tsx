"use client";

import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { SparklesIcon, LoaderCircle, Download, Icon, Loader2, ImagePlus, XCircle } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { coinsStack } from "@lucide/lab";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { Dropzone } from "@/components/ui/kibo-ui/dropzone";
import { uploadFile } from "@/lib/file/upload-file";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { MediaResultStatus } from "@/@types/media/media-type";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_GEN_IMAGE_PAGE } from "@/lib/track-events";

export default function GPTImageClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [prompt, setPrompt] = useState("");
	const [originImage, setOriginImage] = useState<{
		url: string;
		base64: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);

	const handleLocalFileDrop = async (files: File[]) => {
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		setPreviewImageBase64(null);
		setOriginImage(null);

		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);

			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			const base64 = await fileToBase64(files[0]);
			setOriginImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_PAGE,
			membership_level: user?.membershipLevel,
			model: "gpt-image-1",
		});

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);

			// request task
			const { status, message, task_status, request_id } = await ofetch("/api/v1/image/gpt", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: promtpTrim,
					size: { ratio: "1:1", width: 1024, height: 1024 },
					numImages: 1,
					image: originImage?.url,
				},
			});
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;
			await new Promise((resolve) => setTimeout(resolve, 4000));

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 1000)); // wait for 1 seconds
				let {
					status: request_status,
					message: request_message,
					taskStatus: reuqest_taskStatus,
					taskError: request_taskError,
					resultUrls,
				} = await ofetch("/api/v1/image/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = reuqest_taskStatus;
				taskError = request_taskError;
				if (resultUrls && resultUrls.length > 0) {
					const base64 = await imageUrlToBase64(resultUrls[0], { noCache: true });
					setPreviewImageBase64(base64);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Generate image failed. Please try again or contact support.");
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="w-full rounded-lg bg-zinc-100 px-4 pt-2 pb-2">
				<div className="flex flex-col gap-2 sm:flex-row">
					<Textarea
						placeholder="Upload an image, describe the edit, and click Generate...."
						value={prompt}
						maxLength={1500}
						onChange={(e) => setPrompt(e.target.value)}
						className="min-h-[88px] resize-none border-0 px-0 shadow-none focus-visible:ring-0"
					/>
				</div>

				<div className="mt-2 flex flex-wrap items-end justify-between gap-2">
					<div
						onClick={() => {
							if (!session) {
								setSignInBoxOpen(true);
							}
						}}
					>
						{originImage ? (
							<div className="group relative aspect-square w-[60px] shrink-0">
								<img
									src={originImage.base64}
									alt="Model"
									className="h-full w-full rounded-md object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									className="absolute -top-1.5 -right-1.5 z-10 rounded-full bg-black/60 text-white hover:bg-black/70"
									onClick={(e) => {
										e.stopPropagation();
										setOriginImage(null);
									}}
								>
									<XCircle className="size-5" />
								</button>
							</div>
						) : (
							<Dropzone
								multiple={false}
								maxFiles={1}
								noClick={!session}
								onDragEnter={() => {
									if (!session) setSignInBoxOpen(true);
								}}
								onDrop={(files) => handleLocalFileDrop(files)}
								accept={{
									"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
								}}
								className="text-muted-foreground aspect-square h-[60px] w-[60px] shrink-0 border bg-white p-0 hover:bg-zinc-200"
							>
								{uploadingImage ? <Loader2 className="animate-spin" /> : <ImagePlus />}
							</Dropzone>
						)}
					</div>
					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0}
						className="bg-foreground hover:bg-foreground/80"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{session && (
							<p className="flex flex-row items-center gap-0.5 text-xs font-normal [&_svg]:size-3">
								(<span>8</span> <Icon iconNode={coinsStack} />)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>
			{(previewImageBase64 || submitting) && (
				<div className="group relative flex aspect-square w-full max-w-md items-center justify-center rounded-lg bg-zinc-100">
					{submitting && (
						<p className="mx-auto flex w-full flex-col items-center text-center text-lg text-zinc-500">
							<LoaderCircle className="size-7 animate-spin" />
							<span className="font-mono tabular-nums">{seconds}s</span>
						</p>
					)}
					{previewImageBase64 && (
						<img
							src={previewImageBase64}
							alt="Generated image"
							className="h-full w-full rounded-lg object-contain"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
						/>
					)}
					<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
						{previewImageBase64 && (
							<Hint label="Download image">
								<div className="relative">
									<SubmitButton
										isSubmitting={downloading}
										disabled={!previewImageBase64}
										size="icon"
										variant="outline"
										onClick={async () => {
											try {
												setDownloading(true);
												if (userHasPaid) {
													await downloadImageFromBase64(previewImageBase64);
												} else {
													await downloadImageFromBase64WithWatermark(previewImageBase64);
												}
											} catch (error) {
												console.error("Failed to download image:", error);
											} finally {
												setDownloading(false);
											}
										}}
									>
										<Download />
									</SubmitButton>
								</div>
							</Hint>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
