"use client";

import { Comparison, Comparison<PERSON><PERSON>le, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { cn } from "@/lib/utils";
import { TerminalIcon } from "lucide-react";

export default function FluxKontextExamples({
	examples,
}: {
	examples: {
		imageBefore: string;
		imageAfter: string;
		prompt: string;
		size?: string;
	}[];
}) {
	return (
		<div className="grid w-full grid-cols-1 items-center gap-6 sm:grid-cols-2 md:grid-cols-3">
			{examples.map((example, index) => (
				<div key={index} className="bg-muted flex h-full flex-col gap-4 rounded-xl p-4">
					<Comparison className={`${example.size ?? "aspect-3/2"} rounded-lg`}>
						<ComparisonItem position="right" className="">
							<div className={cn("relative aspect-3/2", example.size)}>
								<img
									src={example.imageBefore}
									alt={`Flux Kontext example ${index + 1} before`}
									className="h-full w-full rounded object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								{/* <div
									className="absolute left-2 top-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
									style={{ "--content": "'Before'" } as React.CSSProperties}
								></div> */}
							</div>
						</ComparisonItem>
						<ComparisonItem position="left" className="">
							<div className={cn("relative aspect-3/2", example.size)}>
								<img
									src={example.imageAfter}
									alt={`Flux Kontext example ${index + 1} after`}
									className="h-full w-full rounded object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								{/* <div
									className="absolute right-2 top-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
									style={{ "--content": "'After'" } as React.CSSProperties}
								></div> */}
							</div>
						</ComparisonItem>
						<ComparisonHandle />
					</Comparison>

					<div className="rounded-md text-xs md:block md:text-sm">
						<p className="flex flex-row items-center gap-1.5 pb-1 font-medium">
							<TerminalIcon className="text-action h-3.5 w-3.5" />
						</p>
						<p className="text-secondary-foreground">{example.prompt}</p>
					</div>
				</div>
			))}
		</div>
	);
}
