import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { cn } from "@/lib/utils";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { House } from "lucide-react";
import PageImageClient from "../../_components/page-image.client";
import { FLUX_1_KREA } from "@/lib/utils-image-model";
import { HowToUse } from "@/components/landing/how-to-use";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export const metadata: Metadata = {
	title: `Flux Krea: Try Flux.1 Krea AI Image Generator| ${WEBNAME}`,
	description:
		"Generate stunning photorealistic images with Flux.1 Krea. Create unique visuals that stand out with <PERSON>rea's advanced AI-powered image generation tool.",
	alternates: {
		canonical: "/models/image/flux/flux-krea",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/", icon: House },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
						{ title: "Flux", href: "/models/image/flux" },
					]}
					current={"Flux.1 Krea"}
				/>
			</div>

			<section>
				<div className="relative pt-16 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 text-center sm:mx-auto lg:mr-auto">
							<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Generate Photorealistic Images with Flux.1 Krea</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<a
									href="https://huggingface.co/black-forest-labs/FLUX.1-Krea-dev"
									rel="noopener noreferrer nofollow"
									target="_blank"
									className="text-action underline underline-offset-4"
								>
									Flux.1 Krea
								</a>{" "}
								is a state-of-the-art AI image model developed by Krea and Black Forest Labs, designed to generate stunning, photorealistic
								images from text prompts. Say goodbye to the typical “AI look” and experience natural, vibrant visuals with exceptional detail
								and dynamic color. Try{" "}
								<NoPrefetchLink href={`/ai-image-generator?model=${FLUX_1_KREA.id}`} className="text-action underline underline-offset-4">
									Flux.1 Krea AI image generator!
								</NoPrefetchLink>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<PageImageClient defaultModelId={FLUX_1_KREA.id} defaultGenType="text-to-image" />
			</div>

			<div className="py-24">
				<div className="container flex flex-col items-center justify-center gap-12 px-6">
					<div className="max-w-4xl text-center">
						<h2 className="text-[32px] font-semibold text-pretty">Why Use Flux.1 Krea?</h2>
						<p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">
							Experience effortless, true-to-life image generation that skips the usual AI artifacts—so every picture feels genuine, polished, and
							ready to captivate.
						</p>
					</div>
					<div className="mx-auto grid grid-cols-2 gap-4 sm:max-w-2xl lg:max-w-full">
						{[
							{
								title: "Photorealistic Image Quality",
								description:
									'Flux.1 Krea produces images with exceptional realism. Unlike other AI models, it avoids the typical "AI look" by generating images with natural details, accurate textures, and lifelike lighting—perfect for users who need high-quality visuals without imperfections like blurry backgrounds or unnatural features.',
								image: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-krea/image-1.webp`,
								imageAlt: "all the best models in Dreampik",
							},
							{
								title: "Distinctive, Artistic Aesthetics",
								description:
									"Flux.1 Krea doesn’t just replicate reality—it redefines it with its unique artistic style. Designed to deliver stunning and aesthetically rich visuals, this model ensures that your images stand out with expressive colors, dynamic compositions, and a level of detail that feels fresh and original.",
								image: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-krea/image-2.webp`,
								imageAlt: "generate images in one place",
							},
							{
								title: 'Eliminate the "AI Look"',
								description:
									"Say goodbye to the waxy skin and overly saturated textures often associated with AI-generated images. Flux.1 Krea’s fine-tuning focuses on producing visuals that are far beyond the typical AI results, ensuring your images look genuinely realistic and engaging, every time.",
								image: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-krea/image-3.webp`,
								imageAlt: "text to image in seconds",
							},
							{
								title: "Fine-Tuned for Realistic Visuals",
								description:
									"Built on the robust Flux.1 Dev model, Flux.1 Krea is specially fine-tuned to prioritize realism and natural detail. This means you get photos that are not only accurate but also dynamic and visually compelling—perfect for any project that needs to capture true-to-life beauty without compromising on quality.",
								image: `${OSS_URL_HOST}mkt/pages/image-model/flux/flux-krea/image-4.webp`,
								imageAlt: "easy to use ai image generator in Dreampik",
							},
						].map((feature, index) => (
							<div key={index} className={cn("bg-muted rounded-xl")}>
								<div className="flex h-full flex-col">
									<div className="flex h-[180px] overflow-hidden rounded-t-lg sm:h-[220px] md:h-[240px]">
										<img src={feature.image} alt={feature.imageAlt} className="h-full w-full object-cover" />
									</div>
									<div className="flex flex-col justify-start px-4 py-3 text-center md:px-8 md:py-6 lg:text-start">
										<h3 className="mb-2 text-lg font-medium">{feature.title}</h3>
										<p className="text-muted-foreground text-sm">{feature.description}</p>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			<HowToUse
				title="How to Generate Images with Flux.1 Krea"
				steps={[
					{
						title: "1. Describe",
						description:
							"Describe the image you want in clear, simple language. Add any details, style, or mood you prefer. Flux.1 Krea transforms your ideas into photorealistic visuals.",
					},
					{
						title: "2. Generate",
						description:
							"Click “Generate” to start the process. In seconds, Flux.1 Krea produces a high-quality image based on your prompt, free from the usual “AI look.”",
					},
					{
						title: "3. Download",
						description:
							"Download your finished image with one click. Use it in your projects, share it online, or save it for later—your creativity made real.",
					},
				]}
			/>

			<FAQsComponent
				title="AI Image Generator Related FAQs"
				faqs={[
					{
						question: "What is Flux.1 Krea?",
						answer: "Flux.1 Krea is an advanced AI image generator that lets you create photorealistic images from simple text prompts. Built for realism, it focuses on natural details and expressive colors, so your visuals don’t have that typical 'AI look.'",
					},
					{
						question: "How is Flux.1 Krea different from Flux.1 Dev?",
						answer: "Flux.1 Krea is a fine-tuned version of the base Flux.1 Dev model. While Flux.1 Dev is a general-purpose, open-source text-to-image AI, Flux.1 Krea is specially optimized for photorealism and artistic aesthetics—perfect for anyone who wants beautiful, realistic images right out of the box, without needing to tweak prompts or settings.",
					},
					{
						question: "What types of images can Flux.1 Krea generate?",
						answer: "Flux.1 Krea can generate a wide variety of images across many styles, but it really shines when you want photorealism. Whether you need portraits, landscapes, objects, or creative scenes, you get results with natural textures, dynamic angles, and true-to-life details—no more waxy skin or blurry backgrounds.",
					},
					{
						question: "What are some common use cases for Flux.1 Krea?",
						answer: "People use Flux.1 Krea for all kinds of creative needs—social media content, marketing visuals, concept art, mood boards, storyboards, product mockups, and more. If you need high-quality, realistic images fast, Flux.1 Krea is a go-to solution.",
					},
					{
						question: "Is Flux.1 Krea free to use?",
						answer: "Yes! Flux.1 Krea is open-source and free—you can deploy it yourself at no cost. Or, if you prefer a ready-to-use platform, you can use Flux.1 Krea on Dreampik, where we provide free credits to get you started. That way, you can try all the features without any upfront cost.",
					},
					{
						question: "How long does it take to generate an image with Flux.1 Krea?",
						answer: "It’s lightning-fast—most images are generated in just a few seconds. You don’t have to wait around to see your creative ideas come to life.",
					},
					{
						question: "Where can I access Flux.1 Krea?",
						answer: "Flux.1 Krea is available on several platforms: Black Forest Labs, Krea AI, and Dreampik. With Dreampik, you get the added benefit of using Flux.1 Krea alongside multiple other cutting-edge AI image models, all in one place.",
					},
					{
						question: "Can I use images from Flux.1 Krea for commercial purposes?",
						answer: "Absolutely! If you’re on a paid plan, you have full commercial rights to use the images you generate with Flux.1 Krea—perfect for your business, brand, or any project where licensing matters.",
					},
				]}
			/>

			<FinalCTA
				title="Unleash Your Imagination with Flux.1 Krea"
				description="Leave behind generic, artificial images. Flux.1 Krea empowers you to make visuals that feel authentic and full of life. Enjoy the thrill of seeing your imagination realized in beautiful, realistic detail."
				ctaText="Start with Flux.1 Krea Free"
				ctaTextDisplay={true}
				ctaUrl={`/ai-image-generator?model=${FLUX_1_KREA.id}`}
				ctaClassName="rounded-full"
			/>
		</main>
	);
}
