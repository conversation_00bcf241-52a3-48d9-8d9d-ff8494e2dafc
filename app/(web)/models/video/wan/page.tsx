import type { <PERSON>ada<PERSON> } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { House } from "lucide-react";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import PageVideoClient from "../_components/page-video.client";
import { WAN_2_2_TURBO } from "@/lib/utils-video-model";
import FAQsComponent from "@/components/landing/faqs";
import FeaturesVideoComponent from "@/components/landing/features-video";
import FinalCTA from "@/components/landing/final-cta";

export const metadata: Metadata = {
	title: `Wan 2.2: Try Wan AI Video Generator | ${WEBNAME}`,
	description:
		"Experience Wan 2.2 AI Video Generator—open-source, cinematic-quality videos from text or images. Created by <PERSON><PERSON><PERSON>, Wan AI is fast, powerful, and free to use. Try it now!",
	alternates: {
		canonical: "/models/video/wan",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/", icon: House },
						{ title: "AI Video Generator", href: "/ai-video-generator" },
					]}
					current={"Wan"}
				/>
			</div>

			<section>
				<div className="relative pt-8 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto lg:mr-auto">
							<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Wan 2.2 AI Video Generator</h1>
							<div className="text-muted-foreground mx-auto">
								<a href="https://huggingface.co/Wan-AI" target="_blank" className="text-action underline underline-offset-4">
									Wan AI
								</a>{" "}
								Video Generator is an advanced open-source tool developed by Alibaba’s Tongyi Lab. Wan transforms your text or images into
								stunning, cinematic videos—quickly and easily. The newest version,{" "}
								<a href="https://github.com/Wan-Video/Wan2.2" target="_blank" className="text-action underline underline-offset-4">
									Wan 2.2
								</a>
								, was released on July 28, 2025, offering smarter motion, higher resolution, and creative special effects for everyone. Try the{" "}
								<NoPrefetchLink href={`/image-to-video?model=${WAN_2_2_TURBO.id}`} className="text-action underline underline-offset-4">
									Wan AI Video Generator today!
								</NoPrefetchLink>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<PageVideoClient defaultModelId={WAN_2_2_TURBO.id} />
			</div>

			<FeaturesVideoComponent
				ctaText="Try Wan 2.2 Now"
				ctaUrl={`/ai-video-generator?model=${WAN_2_2_TURBO.id}`}
				ctaClassName="rounded-full"
				features={[
					{
						title: "Effortless Cinematic Video Creation",
						description:
							"With Wan AI Video Generator, you can bring your creative vision to life in just a few clicks. Whether you start with simple text or an image, Wan AI transforms your ideas into stunning, movie-quality videos—no filmmaking experience needed. Instantly create professional content for social media, marketing, or storytelling with ease and confidence.",
						media: `${OSS_URL_HOST}mkt/pages/video-model/wan/wan2-2/feature-sample-1.mp4`,
					},
					{
						title: "Crystal-Clear Motion and Realism",
						description:
							"Wan AI 2.2 Video Generator brings your videos to life with smooth, natural movements and breathtaking detail. Its advanced technology expertly captures complex actions and subtle scenes, so every frame feels real and immersive. Let your stories flow seamlessly, impressing your audience every time.",
						media: `${OSS_URL_HOST}mkt/pages/video-model/wan/wan2-2/feature-sample-2.mp4`,
					},
					{
						title: "Full Creative Control—Just Like a Director",
						description:
							"Take charge of every visual detail with Wan AI Video Generator. From lighting and camera angles to color and composition, you hold the reins. The intuitive interface lets you fine-tune aesthetics for that perfect cinematic look—empowering you to shape every scene, emotion, and message exactly as you imagine.",
						media: `${OSS_URL_HOST}mkt/pages/video-model/wan/wan2-2/feature-sample-3.mp4`,
					},
					{
						title: "Intuitive Multi-Scene & Multi-Object Generation",
						description:
							"Wan AI Video Generator empowers you to build rich, dynamic stories with ease. Effortlessly craft videos featuring multiple scenes and complex environments, complete with several characters or objects interacting smoothly. Your creative ideas—no matter how complex—are faithfully transformed into compelling, cohesive video content.",
						media: `${OSS_URL_HOST}mkt/pages/video-model/wan/wan2-2/feature-sample-4.mp4`,
					},
				]}
			/>

			<FAQsComponent
				title="AI Image Generator Related FAQs"
				faqs={[
					{
						question: "What is Wan AI?",
						answer: "Wan AI is an advanced, open-source AI model that turns your text or images into stunning, cinematic-quality videos. Developed by Alibaba’s Tongyi Lab, it’s designed to help creators, marketers, and businesses bring their ideas to life—fast, effortlessly, and with professional polish.",
					},
					{
						question: "What are the key features of Wan 2.2?",
						answer: "Wan 2.2 packs powerful upgrades: cinematic-level aesthetic control (lighting, color, composition), high-quality motion generation, advanced special effects, precise scene understanding, and support for both text-to-video and image-to-video creation. It’s also optimized to run efficiently on consumer hardware like the Nvidia RTX 4090—no need for expensive cloud servers.",
					},
					{
						question: "How is Wan 2.2 improved from previous versions?",
						answer: "Wan 2.2 raises the bar with a new MoE (Mixture-of-Experts) architecture, delivering even richer, more natural motion and more accurate scene generation. It’s trained on 65% more images and 83% more videos than before, resulting in better generalization, smoother animation, and enhanced creative controls. Special effects, HD output, and faster fine-tuning workflows are all new highlights.",
					},
					{
						question: "What makes Wan 2.2 different from other AI video models?",
						answer: "Wan 2.2 stands out thanks to its cinematic quality, efficient performance on everyday GPUs, open-source flexibility, and deep aesthetic controls. Its unique MoE architecture means higher video quality without extra cost. Compared to many closed-source rivals, Wan 2.2 gives you advanced motion, precise effects, and multi-task versatility—making it perfect for everyone from indie creators to professional filmmakers.",
					},
					{
						question: "What inputs does Wan AI Video Generator support?",
						answer: "You can create videos from both text prompts and images. Just type your idea, upload an image, or combine both—the generator will turn your vision into video. This makes it easy to bring any story, campaign, or concept to life, whether you’re starting with words, pictures, or both.",
					},
					{
						question: "Does Wan AI Video Generator support multiple languages?",
						answer: "Yes, Wan AI Video Generator supports both English and Chinese for prompts and text-to-video creation. This means you can reach broader audiences or create localized content with ease.",
					},
					{
						question: "Can I use the videos for commercial projects?",
						answer: "Absolutely! As long as you’re on a paid plan, you get full commercial rights to use, edit, and publish your generated videos. This includes marketing, social media, ads, and any other business needs—so you can confidently grow your brand with Dreampik’s Wan AI Video Generator.",
					},
				]}
			/>

			<FinalCTA
				title="Bring Your Imagination to Life with Wan AI"
				description="Experience the freedom to turn words or images into breathtaking videos in minutes. Wan AI Video Generator empowers you to create, share, and amaze—no boundaries, just pure creativity. Unleash your vision and see your ideas come alive like never before."
				ctaText="Try Wan 2.2 on Dreampik"
				ctaTextDisplay={true}
				ctaUrl={`/ai-video-generator?model=${WAN_2_2_TURBO.id}`}
				ctaClassName="rounded-full"
			/>
		</main>
	);
}
