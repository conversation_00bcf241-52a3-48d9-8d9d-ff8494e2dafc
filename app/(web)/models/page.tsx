import type { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { imageModelSeries } from "@/config/image-models-config";
import { videoModelSeries } from "@/config/video-models-config";

export const metadata: Metadata = {
	title: `AI Models | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/models",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section className="relative pt-16">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-4 space-y-4 text-center sm:mx-auto lg:mt-8 lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Discover AI Models on Dreampik</h1>
						<div className="text-muted-foreground mx-auto text-lg"></div>
					</div>
				</div>
			</section>

			<div className="space-y-8 py-20">
				<div className="container flex flex-col gap-8 px-6">
					<h2 className="text-lg font-medium text-pretty">Image AI</h2>
					<div className="grid w-full grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
						{imageModelSeries.map((modelSeries, index) => (
							<NoPrefetchLink key={index} href={modelSeries.url} className={cn("group flex flex-row items-center gap-2")}>
								<div className={cn("bg-muted flex h-14 w-14 items-center justify-center rounded-lg")}>
									<img src={modelSeries.logo} className="size-5" alt="" />
								</div>
								<div className="flex flex-col">
									<p className="text-base font-medium">{modelSeries.name}</p>
									{modelSeries.description && <p className="text-muted-foreground text-sm font-[350]">{modelSeries.description}</p>}
								</div>
							</NoPrefetchLink>
						))}
					</div>
				</div>
			</div>

			<div className="space-y-8 pb-20">
				<div className="container flex flex-col gap-8 px-6">
					<h2 className="text-lg font-medium text-pretty">Video AI</h2>
					<div className="grid w-full grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
						{videoModelSeries.map((modelSeries, index) => (
							<NoPrefetchLink key={index} href={modelSeries.url} className={cn("group flex flex-row items-center gap-2")}>
								<div className={cn("bg-muted flex h-14 w-14 items-center justify-center rounded-lg")}>
									<img src={modelSeries.logo} className="size-5" alt="" />
								</div>
								<div className="flex flex-col">
									<p className="text-base font-medium">{modelSeries.name}</p>
									{modelSeries.description && <p className="text-muted-foreground text-sm font-[350]">{modelSeries.description}</p>}
								</div>
							</NoPrefetchLink>
						))}
					</div>
				</div>
			</div>
		</main>
	);
}
