import type { Metada<PERSON> } from "next";
import { buttonVariants } from "@/components/ui/button";
import { PrefetchLink } from "@/components/ui/custom/prefetch-link";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { cn } from "@/lib/utils";
// import { Announcement, AnnouncementTag, AnnouncementTitle } from "@/components/ui/kibo-ui/announcement";
import { ArrowRightIcon } from "lucide-react";
import { GridModels, GridTools } from "./grid-tools";
import { imageAITools, imageModels, videoAITools, videoModels } from "@/config/app-tools";
import { HomeMarqueeDemo } from "./home-marquee-demo";
import ImageGenClient from "@/components/app/image-gen.client";
import { Example } from "@/components/landing/example";

export const metadata: Metadata = {
	title: `${WEBNAME} - AI Image & Video Creation Platform`,
	description: "Your AI image and video creation platform with Dreampik.",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-16 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="mt-8 space-y-4 text-center sm:mx-auto">
							{/* <Link href="/flux/flux-kontext" className="cursor-pointer">
								<Announcement themed className="mb-4 bg-emerald-100 text-emerald-700">
									<AnnouncementTag className="group-[.announcement-themed]:bg-white/40">Latest update</AnnouncementTag>
									<AnnouncementTitle>
										FLUX.1 Kontext [dev] is now available!
										<ArrowUpRightIcon size={16} className="shrink-0 opacity-70" />
									</AnnouncementTitle>
								</Announcement>
							</Link> */}
							<h1 className="mx-auto max-w-4xl text-4xl font-semibold md:text-5xl">AI Image & Video Creation Platform</h1>
							<div className="text-muted-foreground mx-auto text-lg">Create AI images and videos in one place from Dreampik AI</div>
						</div>

						<div className="mt-8">
							<ImageGenClient />
						</div>
					</div>
				</div>
			</section>

			{/* <HomeMarqueeDemo /> */}

			<div className="bg-muted space-y-8 border-b py-20">
				<GridTools title={imageAITools.title} tools={imageAITools.tools} />
				<GridModels models={imageModels} description="Our image AIs support various leading-edge models. They include:" />
			</div>

			<div className="bg-muted space-y-8 py-20">
				<GridTools title={videoAITools.title} tools={videoAITools.tools} />
				<GridModels models={videoModels} description="Our video AIs support various leading-edge models. They include:" />
			</div>

			<Example
				title="Get Inspired"
				description="Get inspired by what others are creating with Dreampik"
				// className="-mt-16"
				columnClassName="lg:columns-5 md:columns-4 sm:columns-3"
				images={[
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: 'A Ghibli-inspired scene featuring a giant, friendly creature walking through a lush forest and holding a sign saying "HiDream on dreampik.art", with vibrant colors, soft lighting, and whimsical details like floating lights and colorful, magical creatures. The scene captures a peaceful, enchanted atmosphere',
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "An armadillo in a rocket at countdown preparing to blast off to Mars, in the style of ukiyo-e",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "A majestic waterfall cascading down a cliff in a dense jungle, with mist rising and sunlight filtering through the canopy, creating a rainbow effect, landscape photography",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "Cinematic still image glowing portrait in a surreal world with shining red grass, minimal soft studio light photography, fine art photo, dark red and white, minimal outfit, redscale color light scheme",
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_1.webp`,
						prompt: 'The text "Imagen-4" in front of a photo, center middle. The photo: Create a cinematic, photorealistic medium shot capturing the nostalgic warmth of a late 90s indie film. The focus is a young woman with brightly dyed pink-gold hair and freckled skin, looking directly and intently into the camera lens with a hopeful yet slightly uncertain smile, she is slightly off-center. She wears an oversized, vintage band t-shirt that says "Dreampik.art" (slightly worn) over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight streaming through a slightly dusty window, creating lens flare and illuminating dust motes in the air. The background shows a blurred, cluttered bedroom with posters on the wall and fairy lights, rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel',
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_2.webp`,
						prompt: "Beach scene with orange-striped umbrellas and chairs, hanging oranges, and a calm sea under a clear blue sky.",
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_3.webp`,
						prompt: "A pair of AirPods Max reimagined in a Rococo aesthetic, featuring pastel pink ear cups adorned with delicate pearl patterns and golden filigree swirls. The headband is wrapped in soft satin ribbon, tied into an elegant bow at the top. The headphones rest on a velvet pastel green cushion, surrounded by scattered pearls, miniature golden cherubs, and a backdrop of flowing tulle and softly glowing light",
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_4.webp`,
						prompt: 'A high-resolution photograph of a gold coin featuring the Google logo at the center. The coin should have the year 1998 engraved at the top. Include finely detailed engravings, ornamental border patterns, and authentic coin textures like reeded edges, matte background, and polished raised elements. Add inscriptions of "Google" near the bottom in bold lettering. The design should look like a professionally minted commemorative coin with symmetrical layout, precision detailing, and classic metallic shading, presented on a dark backdrop for contrast.',
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_5.webp`,
						prompt: "Whimsical pink balloons with smiling faces float above a building against a clear blue sky, creating a playful urban scene.",
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_6.webp`,
						prompt: "Blurred city street scene with people walking and cars moving, capturing the dynamic energy of urban life.",
					},
					{
						model: "Imagen 4",
						url: `${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_8.webp`,
						prompt: "A hyper-realistic scene of Minecraft’s Steve jumping mid-air over car rooftops in a busy street in real-life New York City, modern cars, blurred motion, urban chaos, traffic, reflections on vehicles, dynamic action pose, his blocky body casting shadow, chased by a tall terrifying Enderman emerging from a shadowy alley behind him, dramatic lighting, gritty cinematic atmosphere, real buildings and traffic lights, dusk sky, cinematic lens blur, photorealistic style",
					},
				]}
			/>
		</main>
	);
}
