import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import PhotoEffectClient from "../_components/photo-effect.client";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `AI Restyler: Photo to Restyle | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/photo-effects/restyler",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Photo Effects", href: "/photo-effects" },
					]}
					current={"Photo Restyler"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Photo Style Transformer</h1>
						<div className="text-muted-foreground mx-auto mt-4 md:text-lg"></div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap after:content-(--content)`)}>
							Photo to Ghibili Style Now
						</Link>
					</div> */}
				</div>
			</div>

			<div className="pb-20">
				<PhotoEffectClient />
			</div>
		</main>
	);
}
