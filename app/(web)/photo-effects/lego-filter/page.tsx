import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import PhotoEffectClient from "../_components/photo-effect.client";
import { PhotoStyleID } from "@/lib/utils-photo-effects";
import FeaturesText from "@/components/landing/features-text";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `AI Lego Filter | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/photo-effects/lego-filter",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Photo Effects", href: "/photo-effects" },
					]}
					current={"Lego Filter"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Lego Filter</h1>
						<div className="text-muted-foreground mx-auto mt-4"></div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), "h-12 rounded-full text-base text-nowrap")}>
							Apply Lego Filter to Your Photo
						</Link>
					</div> */}

					{/* <div className="bg-muted mx-auto mt-12 flex aspect-[3/2] w-full max-w-3xl overflow-hidden rounded-xl">
						<div className="relative">
							<img
								src={`${OSS_URL_HOST}mkt/pages/photo-effects/lego-filter/lego-filter.webp`}
								alt="Baby Filter"
								className="h-full w-full object-contain"
								loading="lazy"
							/>
						</div>
					</div> */}
				</div>
			</div>

			<div className="pb-20">
				<PhotoEffectClient styleId={PhotoStyleID.Lego} />
			</div>
		</main>
	);
}
