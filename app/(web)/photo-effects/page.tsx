import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { allPhotoEffects } from "@/lib/utils-photo-effects";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export const metadata: Metadata = {
	title: `AI Photo Effects & Filters | ${WEBNAME}`,
	alternates: {
		canonical: "/photo-effects",
	},
};

export default async function Page() {
	return (
		<div className="flex h-full min-h-screen w-full flex-col px-4 py-4">
			<div className="mx-auto flex w-full flex-col pt-4 pb-2">
				<h1 className="text-xl font-medium whitespace-nowrap">AI Photo Effects & Filters</h1>
				{/* <p className="text-sm text-zinc-400"></p> */}
			</div>

			<div className="mx-auto grid w-full grid-cols-2 gap-4 pt-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
				{allPhotoEffects.map((style, index) => (
					// <NoPrefetchLink href={item.pageUrl} key={index} className="group w-full hover:cursor-pointer">
					// 	<div className="overflow-hidden rounded-md border bg-zinc-800">
					// 		<img src={item.demoImage} alt="" className="aspect-[3/2] rounded-md object-cover" loading="lazy" />
					// 	</div>
					// 	<div className="py-2 text-sm text-zinc-300 group-hover:text-zinc-300/80">{item.pageName}</div>
					// </NoPrefetchLink>

					<NoPrefetchLink
						key={style.id}
						href={style.pageUrl}
						className="bg-muted hover:bg-muted-foreground/20 flex flex-col rounded-lg transition-all"
					>
						<div className="overflow-hidden rounded-t-md">
							{style.demoImage && <img src={style.demoImage} alt={style.name} className="aspect-video h-full w-full object-cover" />}
						</div>
						<div className="p-2">
							<h3 className="text-sm">{style.pageName}</h3>
						</div>
					</NoPrefetchLink>
				))}
			</div>
		</div>
	);
}
