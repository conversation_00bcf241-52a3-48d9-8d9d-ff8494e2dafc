import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import FeaturesComparisonComponent from "@/components/landing/features-comparison";
import PhotoEffectClient from "../_components/photo-effect.client";
import { PhotoStyleID } from "@/lib/utils-photo-effects";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `Studio Ghibli AI Generator: Photo to Ghibli Art | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/photo-effects/studio-ghibli",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Photo Effects", href: "/photo-effects" },
					]}
					current={"Studio Ghibli"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Ghibli Style Generator</h1>
						<div className="text-muted-foreground mx-auto mt-4 md:text-lg"></div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap after:content-(--content)`)}>
							Photo to Ghibili Style Now
						</Link>
					</div> */}
				</div>
			</div>
			<div className="pb-20">
				<PhotoEffectClient styleId={PhotoStyleID.Ghibli} />
			</div>
		</main>
	);
}
