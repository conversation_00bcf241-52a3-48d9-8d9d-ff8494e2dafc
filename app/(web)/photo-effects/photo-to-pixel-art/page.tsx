import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import FeaturesComparisonComponent from "@/components/landing/features-comparison";
import PhotoEffectClient from "../_components/photo-effect.client";
import { PhotoStyleID } from "@/lib/utils-photo-effects";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `Pixel Art Generator: Photo to Pixel Art Converter | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/photo-effects/photo-to-pixel-art",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Photo Effects", href: "/photo-effects" },
					]}
					current={"Pixel Art Generator"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">Convert Photo to Pixel Art</h1>
						<div className="text-muted-foreground mx-auto mt-4 md:text-lg"></div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap after:content-(--content)`)}>
							Photo to Pixel Art Now
						</Link>
					</div> */}

					{/* <div className="mx-auto mt-12 flex w-full max-w-4xl rounded"></div> */}
				</div>
			</div>

			<div className="pb-20">
				<PhotoEffectClient styleId={PhotoStyleID.Pixel} />
			</div>
		</main>
	);
}
