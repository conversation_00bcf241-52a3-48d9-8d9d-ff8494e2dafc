import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import PhotoEffectClient from "../_components/photo-effect.client";
import { PhotoStyleID } from "@/lib/utils-photo-effects";
import FeaturesText from "@/components/landing/features-text";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `AI Baby Filter: Turn Yourself into a Baby Face | ${WEBNAME}`,
	description:
		"Transform your photos into adorable baby faces with our AI Baby Filter. Instantly create cute, shareable baby versions of yourself—no editing skills needed!",
	alternates: {
		canonical: "/photo-effects/baby-filter",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "Photo Effects", href: "/photo-effects" },
					]}
					current={"Baby Filter"}
				/>
			</div>

			<div className="relative pt-8 pb-12">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Baby Filter: Turn Yourself into a Baby Face</h1>
						<div className="text-muted-foreground mx-auto mt-4">
							Curious how adorable you’d look as a baby? The AI Baby Filter lets you transform your selfie into a heartwarming, realistic baby
							version in seconds. Upload your photo, and our powerful AI automatically gives you those cute cheeks, big bright eyes, and youthful
							glow—no editing skills required. Relive the playful magic of childhood and create something truly shareable!
						</div>
					</div>

					{/* <div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), "h-12 rounded-full text-base text-nowrap")}>
							Add a Baby Filter to Your Photo
						</Link>
					</div> */}

					{/* <div className="bg-muted mx-auto mt-12 flex aspect-[3/2] w-full max-w-3xl overflow-hidden rounded-xl">
						<div className="relative">
							<img
								src={`${OSS_URL_HOST}mkt/pages/photo-effects/baby-filter/baby-filter.avif`}
								alt="Baby Filter"
								className="h-full w-full object-contain"
								loading="lazy"
							/>
						</div>
					</div> */}
				</div>
			</div>

			<div className="pb-20">
				<PhotoEffectClient styleId={PhotoStyleID.BabyFilter} />
			</div>

			<FeaturesText
				features={[
					{
						title: "Relive Childhood Joy in Seconds",
						description:
							"Our AI Baby Filter instantly transforms your photo into a heartwarming baby version. Upload a clear, front-facing portrait, and advanced AI maps your facial features, adding larger eyes, softer skin, and rounder cheeks for a realistic, nostalgic result you’ll love.",
					},
					{
						title: "Go Viral with Shareable Baby Pics",
						description:
							"Create adorable, baby-faced images that are perfect for social media. With one click, our AI Baby Filter generates a photo ready to share on TikTok, Instagram, or beyond, driving likes, comments, and smiles from friends and followers.",
					},
					{
						title: "Effortless Creativity for Everyone",
						description:
							"No editing skills? No problem! Our AI Baby Filter automates the entire process—just upload your photo, and in moments, you’ll have a polished, baby-faced image that looks professional and ready to share, no expertise needed.",
					},
					{
						title: "Keep Your Memories Private and Secure",
						description:
							"Enjoy peace of mind while having fun. Our AI Baby Filter ensures your photos and edits stay private and secure, so you can focus on the joy of transforming your image without worrying about data privacy.",
					},
				]}
			/>

			<HowToUse
				title="How to Use the AI Baby Filter"
				steps={[
					{
						title: "1. Upload Your Photo",
						description:
							"Choose a clear photo of yourself and upload it to our Baby Filter tool. This helps our AI detect your features for the best baby face transformation.",
					},
					{
						title: "2. Click Generate",
						description:
							"Press the 'Generate' button. Our AI will instantly apply the Baby Filter and show you your adorable, baby-like version in seconds.",
					},
					{
						title: "3. Download and Share",
						description: "Download your new baby face image. Share it on social media or with friends to surprise and delight everyone!",
					},
				]}
			/>

			<FAQsComponent
				title="AI Baby Filter Related FAQs"
				faqs={[
					{
						question: "What is the AI Baby Filter?",
						answer: "The AI Baby Filter is a state-of-the-art AI tool that instantly transforms your uploaded photo into an adorable baby version of yourself. Whether you want a bit of fun, nostalgia, or social sharing, the Baby Filter makes it effortless.",
					},
					{
						question: "What are the key features of the Baby Filter?",
						answer: "Our Baby Filter offers a range of standout features: it delivers lightning-fast results (in less than 5 seconds!), produces incredibly realistic and high-quality baby-faced images, and boasts a user-friendly interface requiring zero technical experience. Your photos stay private and secure, and you can easily download or share your cute new look. Adorable, convincing results are always just one upload away.",
					},
					{
						question: "How can I get the best results with the Baby Filter?",
						answer: "For the most charming and accurate baby-faced effects, upload a high-quality, clear photo. Ideally, your photo should be front-facing, with good lighting and a simple background. Avoid blurry or dark images—these can reduce accuracy. Remember: the clearer the photo, the cuter and more lifelike your baby transformation will be!",
					},
					{
						question: "How does the AI Baby Filter ensure accurate and realistic results?",
						answer: "Our AI Baby Filter uses an advanced algorithm that carefully studies facial structure and unique features. By mapping out the important details of your face, it ensures your baby version isn’t just cute—it truly looks like you, just much younger! The combination of cutting-edge AI and thousands of hours of training guarantees the transformation is both believable and heartwarming.",
					},
					{
						question: "What can I use the AI Baby Filter for?",
						answer: "There are endless ways to have fun with the Baby Filter! Share your transformation on social media for some viral laughs, use it in creative projects or family keepsakes, or create unique marketing stories and digital content. It’s a fantastic tool for entertainment, nostalgia, role-playing, storytelling, or simply seeing what you or your loved ones would have looked like as a baby.",
					},
					{
						question: "Who can benefit from using the Baby Filter?",
						answer: "Anyone and everyone! Social media enthusiasts, content creators, and creative professionals adore using baby filters for engaging, lighthearted content. Parents, families, and friends often use it for sentimental keepsakes or gifts. Marketers can craft attention-grabbing campaigns, and anyone curious about their “baby self” will love playing with this tool. If you crave fun, cuteness, or a trip down memory lane, the Baby Filter is for you.",
					},
					{
						question: "How does our AI Baby Filter compare to other tools?",
						answer: "Our AI Baby Filter stands out with industry-leading person-to-person likeness, ultra-fast processing speeds (less than 5 seconds!), and unmatched output quality. Unlike many other filters, our tool doesn’t require technical ability—just upload your photo and let the AI work its magic. The result is always a hyper-realistic, truly personal baby transformation that’s easy and enjoyable.",
					},
					{
						question: "Can I share my baby-faced photos? Where can I use them?",
						answer: "Absolutely! Once your baby photo is generated, you can download and share it anywhere—Facebook, Instagram, TikTok, WhatsApp, or any favorite social platform. Bring smiles to friends and family, join the latest online trends, or use your baby-faced image in creative projects. With one click, your adorable new look is ready to charm the world!",
					},
				]}
			/>

			<FinalCTA
				ctaText="Try the Baby Filter Now"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
				title="Ready to Meet Your Baby-Faced Self?"
				description="Unlock the magic of our AI Baby Filter and transform your photo into an adorable baby version that sparks joy, nostalgia, and connection. Whether you’re chasing viral fame, reminiscing about childhood, or dreaming of your future baby, our tool delivers instant, realistic results that feel like pure magic. Upload your photo today and step into a world of smiles and wonder!"
			/>
		</main>
	);
}
