import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import ImageGeneratorsClient from "../_components/image-generators.client";

export const metadata: Metadata = {
	title: `AI Banner Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/features/ai-banner-generator",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="pb-20">
			<div className="">
				<ImageGeneratorsClient
					title="AI Banner Generator"
					defaultTool="ai-banner-generator"
					defaultPrompt="Minimalist banner with clean lines and a monochromatic color scheme. [subject]"
				/>
			</div>

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Banner Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create banner images with AI</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
