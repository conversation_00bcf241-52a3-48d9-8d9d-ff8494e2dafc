import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import ImageGeneratorsClient from "../_components/image-generators.client";
import { FLUX_1_KREA } from "@/lib/utils-image-model";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import FeaturesComponent from "@/components/landing/features";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ArrowRightIcon } from "lucide-react";
import { HowToUse } from "@/components/landing/how-to-use";
import { ImageMarqueeShow } from "@/components/landing/image-marquee-show";

export const metadata: Metadata = {
	title: `AI Girl Generator - Create Your Dream Character | ${WEBNAME}`,
	description:
		"Generate photorealistic, anime, or dreamy digital girls with our AI girl generator. Customize features like hair, ethnicity, and style. Start crafting your virtual girl now!",
	alternates: {
		canonical: "/features/ai-girl-generator",
	},
};

export default async function Page() {
	return (
		<main className="">
			<div className="">
				<ImageGeneratorsClient
					title="AI Girl Generator"
					defaultTool="ai-girl-generator"
					defaultModelId={FLUX_1_KREA.id}
					defaultPrompt="{style} of a {subject/features} girl, {outfit/accessories}, {pose/expression}, {scene/background}, {composition/lighting}, {quality/details}"
					sampleImage={`${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/demo.webp`}
				/>
			</div>

			<section>
				<div className="relative space-y-20 pt-24 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Girl Generator </h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">
								Generate your dream girl effortlessly using our AI girl generator. Whether photorealistic, anime, or whimsical, each
								detail—hair, face, mood—comes together to create a unique digital girl in moments.
							</div>
						</div>

						<div className="mt-10 flex flex-col items-center justify-center gap-2 md:flex-row">
							<Link
								href="#"
								className={cn(buttonVariants({ size: "lg" }), `bg-action hover:bg-action/80 h-12 rounded-full text-base text-nowrap`)}
							>
								Start Creating AI Girls <ArrowRightIcon className="size-5" />
							</Link>
						</div>
					</div>
					<ImageMarqueeShow
						images={[
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_1.webp`,
								prompt: "Young woman in an orange puffer jacket with sunglasses, gazing directly with a calm expression. Vibrant and stylish portrait.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_2.webp`,
								prompt: "Artistic portrait of a woman with blurred orange light streaks, creating a dreamy and ethereal effect.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_3.webp`,
								prompt: "Asian woman with headphones gazes thoughtfully at a vending machine, illuminated by cool light, in a nighttime urban setting.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_4.webp`,
								prompt: "Elegant woman in a textured blazer with a gold necklace, exuding confidence against a dark background.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_5.webp`,
								prompt: "A woman in a black suit sits on a white chair, holding a magazine featuring her image, smiling confidently in a minimalist setting.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_6.webp`,
								prompt: "Chic woman in coral pants and beige blazer, accessorized with red sunglasses and a woven clutch, walking in an urban setting.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_7.webp`,
								prompt: "Stylish Asian woman in red sunglasses and shiny blue jacket exudes futuristic elegance. Bold red lipstick and gold earrings complete the look.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_8.webp`,
								prompt: "A smiling woman in a cozy kitchen holds a neatly wrapped gift box, surrounded by rustic decor and warm tones.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_9.webp`,
								prompt: "Young girl in retro room with vintage suitcase and radio, wearing white sweater and orange skirt. Nostalgic and serene setting.",
							},
							{
								url: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/example_10.webp`,
								prompt: "Elegant woman with sleek black hair holds a chic beige handbag, wearing a cream coat. Minimalist and sophisticated fashion style.",
							},
						]}
					/>
				</div>
			</section>

			<FeaturesComponent
				ctaText="Start Creating AI Girls"
				ctaUrl="#"
				features={[
					{
						title: "AI Girl Generator with Precise Control",
						description:
							"Stop getting results that are almost right and start creating exactly what you see in your mind. Our AI girl generator gives you the power to direct every element of your image with simple commands. You can guide the camera to get the perfect angle like a close-up or full body shot, paint your scene with cinematic lighting for the right mood, and demand detailed skin texture for a truly realistic AI girl generator experience. Take full control by also telling the AI what to avoid, ensuring every image you create is a masterpiece, made just the way you want it.",
						image: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/feature-1.webp`,
						imageAlt: `${WEBNAME}: AI Girl Generator`,
					},
					{
						title: "AI Girl Photo Generator from Your Selfie",
						description:
							"Discover a new version of yourself by simply uploading a favorite photo. Our AI girl photo generator does more than just apply a filter; it intelligently recognizes your unique features to reimagine you as a completely new character. See yourself as a brave fantasy hero, the star of a vibrant anime, or a stunning digital painting. It’s a magical and personal way to explore other worlds while still seeing a familiar spark of yourself in every creation, making it the perfect AI anime girl generator for a personal touch.",
						image: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/feature-2.webp`,
						imageAlt: `${WEBNAME}: AI Girl Photo Generator from Your Selfie`,
					},
					{
						title: "A Universe of Styles for Your AI Girl",
						description:
							"Why settle for one style when you can invent your own? Go beyond the standard options and become a true creative pioneer. Our advanced AI female image generator allows you to blend different artistic ideas to produce something the world has never seen before. Imagine a photorealistic AI girl living in a watercolor dreamscape, or a character that combines futuristic cyberpunk aesthetics with classical elegance. This digital girl maker is your playground to experiment, mix, and match until you discover a style that is uniquely yours.",
						image: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/feature-3.webp`,
						imageAlt: `${WEBNAME}: Generate AI Girls in Different Styles`,
					},
					{
						title: "AI-Generated Girls for Any Project",
						description:
							"Your creations are ready to go wherever your imagination takes them. This virtual girl creator is designed to support all your creative needs, big or small. Use the AI dream girl generator to craft an unforgettable social media avatar, quickly visualize character concepts for your novel or game, design a beautiful and personalized piece of art for a friend, or simply build an inspiration board to kickstart your next big idea. Whatever your project, you can now generate the perfect visual in seconds.",
						image: `${OSS_URL_HOST}mkt/pages/image-generators/ai-girl-generator/feature-4.webp`,
						imageAlt: `${WEBNAME}: AI Generated Girls for Any Project`,
					},
				]}
			/>

			<HowToUse
				title="How to Create AI Girls using Dreampik"
				steps={[
					{
						title: "Step 1: Describe Your Image",
						description:
							"The more detailed you are, the better the result! Think about the features you want your AI girl to have—whether it’s hair color, style, eye shape, skin tone, age, or even her personality. The clearer your description, the more accurate the generated image will be.",
					},
					{
						title: "Step 2: Generate",
						description:
							"Once you’ve provided your description, hit the “Generate” button and watch the magic happen. Our AI works fast to create the girl of your dreams. You can try different variations until you’re 100% satisfied with the result.",
					},
					{
						title: "Step 3: Download",
						description:
							"Once you’ve created your perfect AI girl, download the image in just a few clicks. Whether for personal, creative, or professional use, you’ll have your high-quality, custom-made digital girl ready.",
					},
				]}
			/>

			<FAQsComponent
				title="AI Girl Generator Related FAQs"
				faqs={[
					{
						question: "What is an AI girl generator?",
						answer: "An AI girl generator is an online tool powered by artificial intelligence that lets you create digital images of girls in many different styles. Simply describe your ideal character, and the AI will bring her to life—whether you want a realistic portrait, an anime-inspired creation, or something totally unique. It’s fast, easy, and requires no design skills.",
					},
					{
						question: "Can the AI girl generator create photorealistic girls?",
						answer: "Yes! You can generate stunning, photorealistic images of girls that look almost like real photographs. If you’re after lifelike detail and realistic skin texture, just describe exactly what you want—and let the AI handle the rest.",
					},
					{
						question: "How do I generate an AI girl using text?",
						answer: "It’s simple: just type out a description of the girl you want to see. Mention her features, style, pose, outfit, expression, and any other details. The more specific you are, the closer the AI will get to your vision. In just a few clicks, you’ll have a brand new character image made for you.",
					},
					{
						question: "Can I customize the generated AI girl images?",
						answer: "Absolutely. You can adjust every detail, including the girl’s features, outfit, pose, background, and even the lighting or artistic style. Fine-tune your prompts to guide the AI—so your image matches your imagination, not just a preset template.",
					},
					{
						question: "Can I upload my own photo to generate an AI girl?",
						answer: "Yes, you can upload your own photo as a reference image. This way, the AI can create girl images inspired by your unique features or style. For even more consistency in character images, we recommend trying specialized AI models like Flux Kontext for best results.",
					},
					{
						question: "What styles can I generate with the AI girl generator?",
						answer: "You’re not limited to one look. Generate girls in cartoon, anime, photorealistic, watercolor, Disney, cyberpunk, retro, fantasy, or mix styles to invent something new. The possibilities are endless—your creativity sets the boundaries.",
					},
					{
						question: "Can I use the AI girl generator to create characters for my story, game, or project?",
						answer: "Yes, you can generate original characters for any creative project—stories, games, social profiles, or even as art gifts. Just describe the personality or vibe you want, and the AI will visualize your unique character.",
					},
					{
						question: "What is the best female AI generator?",
						answer: "If you’re looking for the best, Dreampik stands out for its creative flexibility, photorealistic options, and ease of use. Whether you want a dreamy anime girl or a lifelike portrait, Dreampik is designed to help you bring your vision to life.",
					},
					{
						question: "Is the AI girl generator free to use?",
						answer: "You can get started for free with our included credits. Explore the features, try out different styles, and create a few images—no payment needed. When you want to unlock more, you can upgrade at any time.",
					},
					{
						question: "Can I use the AI-generated girl images for commercial purposes?",
						answer: "If you’re on a paid plan, you get full commercial rights to the images you create. That means you can use your AI-generated girls for business, branding, merch, or any other professional project.",
					},
				]}
			/>

			<FinalCTA
				title="Bring Your Dream AI Girl to Life in Seconds"
				description="Imagine creating your perfect girl character—exactly as you see her in your mind. With Dreampik’s AI girl generator, you’re just a few clicks away from stunning, lifelike, or anime-inspired girl images. Personalize every detail, blend unique styles, and watch your visions turn into reality."
				ctaText="Create Your Dream Girl"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
			/>
		</main>
	);
}
