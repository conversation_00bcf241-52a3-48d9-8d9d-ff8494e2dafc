"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { RectangleVertical, Square, RectangleHorizontal, Download, LoaderCircle, CoinsIcon, ChevronDownIcon, Check } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromUrl } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { sendGTMEvent } from "@next/third-parties/google";
// import { AppTitleAndBackward } from "../../app-backward";
import { EVENT_GEN_IMAGE_GENERATORS } from "@/lib/track-events";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ReactSVG } from "react-svg";
import { RECRAFT_3 } from "@/lib/utils-image-model";

type ImageSizeType = {
	ratio: string;
	width: number;
	height: number;
	label: string;
	icon: any;
};
const imageSizes: ImageSizeType[] = [
	{ ratio: "1:1", width: 1024, height: 1024, label: "Square", icon: Square },
	{ ratio: "2:3", width: 683, height: 1024, label: "Portrait", icon: RectangleVertical },
	{ ratio: "3:4", width: 768, height: 1024, label: "Traditional", icon: RectangleVertical },
	// { ratio: "4:5", width: 819, height: 1024, label: "Social Post", icon: RectangleVertical },
	{ ratio: "9:16", width: 576, height: 1024, label: "Social Story", icon: RectangleVertical },
	{ ratio: "3:2", width: 1024, height: 683, label: "Standard", icon: RectangleHorizontal },
	{ ratio: "4:3", width: 1024, height: 768, label: "Classic", icon: RectangleHorizontal },
	{ ratio: "16:9", width: 1024, height: 576, label: "Widescreen", icon: RectangleHorizontal },
];

const styles: {
	id: string;
	name: string;
}[] = [
	{ id: "vector_illustration", name: "Auto" },
	{ id: "vector_illustration/bold_stroke", name: "Bold Stroke" },
	{ id: "vector_illustration/chemistry", name: "Chemistry" },
	{ id: "vector_illustration/colored_stencil", name: "Colored Stencil" },
	{ id: "vector_illustration/contour_pop_art", name: "Contour Pop Art" },
	{ id: "vector_illustration/cosmics", name: "Cosmics" },
	{ id: "vector_illustration/cutout", name: "Cutout" },
	{ id: "vector_illustration/depressive", name: "Depressive" },
	{ id: "vector_illustration/editorial", name: "Editorial" },
	{ id: "vector_illustration/emotional_flat", name: "Emotional Flat" },
	{ id: "vector_illustration/engraving", name: "Engraving" },
	{ id: "vector_illustration/infographical", name: "Infographical" },
	{ id: "vector_illustration/line_art", name: "Line Art" },
	{ id: "vector_illustration/line_circuit", name: "Line Circuit" },
	{ id: "vector_illustration/linocut", name: "Linocut" },
	{ id: "vector_illustration/marker_outline", name: "Marker Outline" },
	{ id: "vector_illustration/mosaic", name: "Mosaic" },
	{ id: "vector_illustration/naivector", name: "Naivector" },
	{ id: "vector_illustration/roundish_flat", name: "Roundish Flat" },
	{ id: "vector_illustration/segmented_colors", name: "Segmented Colors" },
	{ id: "vector_illustration/sharp_contrast", name: "Sharp Contrast" },
	{ id: "vector_illustration/thin", name: "Thin" },
	{ id: "vector_illustration/vector_photo", name: "Vector Photo" },
	{ id: "vector_illustration/vivid_shapes", name: "Vivid Shapes" },
];
export default function VectorGeneratorClient() {
	// const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	// const modelParam = searchParams.get("model");
	const [prompt, setPrompt] = useState<string>("");
	const [imageSize, setImageSize] = useState<ImageSizeType>(imageSizes[0]);
	const [style, setStyle] = useState<{
		id: string;
		name: string;
	}>(styles[0]);

	const [previewImage, setPreviewImage] = useState<string | null>(null);

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		sendGTMEvent({
			event: EVENT_GEN_IMAGE_GENERATORS,
			membership_level: user?.membershipLevel,
			tool: "ai-vector-generator",
			model: RECRAFT_3.id,
		});

		try {
			setPreviewImage(null);
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/generators/vector", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: promtpTrim,
					size: {
						ratio: imageSize.ratio,
						width: imageSize.width,
						height: imageSize.height,
					},
					style: style.id,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrls = ["https://static.dreampik.art/dev/media/202507/202507200198286f950e74c2a694e2fd92757ea2.svg"];

			if (resultUrls && resultUrls.length > 0) {
				setPreviewImage(resultUrls[0]);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImage(null);
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="flex min-h-svh w-full flex-col gap-4 overflow-hidden bg-zinc-900 px-4 py-4 md:flex-row">
			<div className="flex w-full shrink-0 flex-col rounded border border-zinc-700 bg-zinc-800 md:h-[90dvh] md:max-w-80 md:min-w-80 xl:max-w-[360px] xl:min-w-[360px]">
				{/* <AppTitleAndBackward title="AI Vector Generator" backUrl="/image-generators" /> */}

				<div
					className={cn(
						"grow space-y-4 overflow-y-auto p-3 lg:p-4",
						"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
					)}
				>
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Prompt</p>
						<Textarea
							placeholder="Describe your image"
							rows={5}
							maxLength={2000}
							className={cn(
								"max-h-36 min-h-24",
								"resize-none border-zinc-700 shadow-none focus-visible:shadow-sm focus-visible:ring-0 dark:bg-zinc-900/50",
								"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>

					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Style</p>
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border border-zinc-700 bg-zinc-900/50 px-3 py-2 text-sm whitespace-nowrap text-zinc-200 shadow-none ring-offset-0 hover:bg-zinc-800",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<span className="text-[13px] font-[350]">{style.name}</span>
								</div>
								<ChevronDownIcon className="-me-1 opacity-60" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width) border-zinc-700 bg-zinc-900">
								{styles.map((styleOption, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] text-zinc-300 focus:bg-indigo-500 focus:text-zinc-200 [&>svg]:size-[14px]"
										onClick={() => {
											setStyle(styleOption);
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<span className="">{styleOption.name}</span>
										</div>
										{styleOption.id === style.id && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Aspect ratio</p>
						<Select
							value={imageSize.ratio}
							onValueChange={(value) => setImageSize(imageSizes.find((size) => size.ratio === value) ?? imageSizes[0])}
						>
							<SelectTrigger className="w-full cursor-pointer border-zinc-700 text-zinc-200 shadow-none ring-offset-0 focus:ring-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800">
								<SelectValue>
									<div className="flex flex-row items-center gap-2">
										<imageSize.icon className="size-5 fill-current text-zinc-600" />
										<span className="text-[13px] font-[350]">{imageSize.ratio}</span>
									</div>
								</SelectValue>
							</SelectTrigger>
							<SelectContent className="bg-zinc-900">
								{imageSizes.map((size, index) => {
									return (
										<SelectItem
											key={index}
											value={size.ratio}
											className="group cursor-pointer bg-zinc-900/50 text-[13px] font-[350] text-zinc-300 focus:bg-indigo-500 focus:text-zinc-200"
										>
											<p className="flex flex-row items-center gap-2">
												<size.icon className="size-5 fill-current text-zinc-600" />
												<span className="">{size.ratio}</span>
												<span className="text-[10px] text-zinc-400 group-hover:text-zinc-300">{size.label}</span>
											</p>
										</SelectItem>
									);
								})}
							</SelectContent>
						</Select>
					</div>
				</div>

				<div className="space-y-0.5 border-t border-zinc-700 p-3 lg:p-4">
					<p className="flex flex-row items-center gap-1 text-[11px] font-[350] text-zinc-400">
						<CoinsIcon className="size-3" />
						12 credits
					</p>
					<SubmitButton
						variant="secondary"
						className="w-full cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !prompt.trim() }}
					>
						Generate
					</SubmitButton>
				</div>
			</div>

			<div
				className={cn(
					"flex min-h-64 w-full items-start justify-center overflow-y-auto rounded bg-zinc-800 p-4 md:h-[90dvh]",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
				)}
			>
				<div className="h-full w-full">
					<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
						<div
							className={cn(
								"group relative mx-auto flex w-full max-w-3xl justify-center rounded",
								(previewImage || submitting) && "aspect-[3/2] border border-zinc-700 bg-zinc-900/50",
							)}
						>
							{previewImage && (
								<>
									<ReactSVG
										src={previewImage}
										className=""
										wrapper="span"
										beforeInjection={(svg) => {
											svg.removeAttribute("width");
											svg.removeAttribute("height");
											svg.setAttribute("class", "w-full h-full");
											svg.setAttribute("preserveAspectRatio", "xMidYMid meet");
										}}
									/>
									<div
										className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white shadow backdrop-blur-xs after:content-(--content)"
										style={{ "--content": "'SVG'" } as React.CSSProperties}
									></div>
									<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
										<Hint label="Download image" sideOffset={10}>
											<div className="relative">
												<SubmitButton
													isSubmitting={downloadingImage}
													disabled={!previewImage}
													size="icon"
													variant="secondary"
													onClick={async () => {
														try {
															setDownloadingImage(true);
															await downloadImageFromUrl(previewImage);
														} catch (error) {
															console.error("Failed to download image:", error);
														} finally {
															setDownloadingImage(false);
														}
													}}
												>
													<Download />
												</SubmitButton>
											</div>
										</Hint>
									</div>
								</>
							)}
							{submitting && (
								<p className="flex aspect-[3/2] h-full w-full max-w-3xl flex-col items-center justify-center text-center text-zinc-400">
									<LoaderCircle className="size-6 animate-spin" />
									<span className="text-sm tabular-nums">{seconds}s</span>
								</p>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
