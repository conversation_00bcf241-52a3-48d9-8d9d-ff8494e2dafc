import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import ImageToVideo from "./image-to-video.client";

export const metadata: Metadata = {
	title: `AI Image to Video Generator: Turn Images to Videos | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/image-to-video",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="">
				<ImageToVideo />
			</div>

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Image to Video Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create AI videos from Dreampik AI✨✨</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
