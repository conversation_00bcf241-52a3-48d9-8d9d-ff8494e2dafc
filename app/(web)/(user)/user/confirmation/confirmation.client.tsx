"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { ofetch } from "ofetch";
import { AuthError, handleError } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";

export function ConfirmationClient() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [isChecking, setIsChecking] = useState(false);
	const [statusMessage, setStatusMessage] = useState("Thank you! Your checkout is now being processed.");
	const [subMessage, setSubMessage] = useState("Please wait a moment, it may take a few seconds or minutes.");

	const checkoutId = searchParams.get("checkout_id");
	useEffect(() => {
		if (checkoutId) {
			checkPaymentStatus(checkoutId);
		}
	}, []);

	const checkPaymentStatus = async (checkoutId: string) => {
		setIsChecking(true);

		let attempts = 0;
		const maxAttempts = 10;
		const delay = (ms: number) => new Promise((r) => setTimeout(r, ms));

		try {
			while (attempts < maxAttempts) {
				attempts++;

				const res = await ofetch<{
					status: number;
					checkoutStatus: "pending" | "succeeded" | "failed";
					message?: string;
				}>("/api/payment/checkout/status", {
					method: "POST",
					body: { checkoutId },
				});

				handleError(res.status, res.message);

				if (res.checkoutStatus === "succeeded") {
					setStatusMessage("Payment successful!");
					setSubMessage("Your payment has been processed successfully.");
					return;
				}
				if (res.checkoutStatus === "failed") {
					break;
				}
				// pending
				if (attempts < maxAttempts) {
					await delay(5000);
					continue;
				} else {
					setSubMessage("Still processing. You can visit the billing page to check later.");
				}
			}
		} catch (error) {
			console.error("Payment status check error:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Error creating a checkout.");
			}
		} finally {
			setIsChecking(false);
		}
	};
	return (
		<Card className="-mt-14 w-full max-w-md p-0 shadow-xl transition-all duration-300 hover:shadow-indigo-800">
			<CardHeader className="p-8">
				<div className="flex flex-col items-center">
					<div className="text-center">
						<p className="text-lg">{statusMessage}</p>
						<p className="text-muted-foreground mt-2 text-sm">{subMessage}</p>
					</div>
				</div>

				<div className="pt-4">
					<Button
						disabled={isChecking}
						variant="secondary"
						className="w-full bg-linear-to-r from-indigo-500 to-indigo-600 transition-all duration-300 hover:from-indigo-500 hover:to-indigo-600"
						onClick={() => router.push("/user/my-billing")}
					>
						{isChecking ? <div className="mx-auto size-4 animate-spin rounded-full border-b-2 border-zinc-200"></div> : "Go to my billing"}
					</Button>
				</div>

				<div className="flex justify-center">
					<div className="text-muted-foreground mt-2 animate-bounce text-xs">✨ Your journey with us begins now ✨</div>
				</div>
			</CardHeader>
		</Card>
	);
}
