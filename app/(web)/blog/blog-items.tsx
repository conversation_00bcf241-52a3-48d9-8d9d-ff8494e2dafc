import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export type BlogItem = {
	slug: string;
	lang: string;
	title: string;
	intro: string;
	createdAt: number;
	image?: string;
	tags?: {
		name: string;
		slug: string;
	}[];
};

export default function BlogItems({ blogHeads, blogCategories }: { blogHeads: any[]; blogCategories: any[] }) {
	return (
		<div className="space-y-8">
			{blogHeads.map((blogHead, index) => (
				<div key={index} className="flex flex-col gap-6 py-4 md:flex-row md:items-center">
					{/*<div className="w-full md:w-1/3">
						<img src={blog.image} alt={blog.title} className="rounded-lg" loading="lazy" />
					</div>*/}
					<div className="w-full space-y-3">
						<NoPrefetchLink href={`/blog/${blogHead.slug}`} className="w-full space-y-2">
							<h2 className="text-lg font-semibold text-primary">{blogHead.title}</h2>
							<p className="line-clamp-2 text-sm text-muted-foreground">{blogHead.intro}</p>
						</NoPrefetchLink>
						<div className="flex items-center space-x-2 text-sm text-gray-500">
							<span className="text-xs">{format(blogHead.publishedAt, "MMM d, yyyy")}</span>
							{blogHead.categoryId && (
								<Badge className="rounded-full font-normal shadow-none">
									{blogCategories.find((category) => category.id === blogHead.categoryId)?.name}
								</Badge>
							)}
						</div>
					</div>
				</div>
			))}
		</div>
	);
}
