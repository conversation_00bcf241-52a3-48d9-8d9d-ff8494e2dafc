import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { getChangelogs } from "@/server/utils-changelog.server";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
	title: `Changelogs | ${WEBNAME}`,
	description: `Latest updates and changes for ${WEBNAME}.`,
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return notFound();
	const { changelogs } = await getChangelogs();
	return (
		<div className="flex w-full grow flex-col">
			<div className="pt-24 lg:pt-32">
				<div className="container max-w-3xl">
					<div className="mx-auto space-y-3">
						<h1 className="mb-4 text-3xl font-bold tracking-tight md:text-5xl">Changelog</h1>
						<p className="text-muted-foreground text-base sm:text-lg">Get the latest updates and improvements to {WEBNAME}.</p>
					</div>
				</div>
			</div>

			<div className="container mx-auto mt-16 mb-32 max-w-3xl space-y-16 md:mt-24">
				{changelogs.map((changelog, index) => (
					<div key={index} className="relative flex flex-col gap-4 md:flex-row md:gap-16">
						<div className="flex flex-row items-center gap-3 md:flex-col">
							<Badge variant="secondary" className="min-w-[80px] justify-center text-base font-normal">
								v{changelog.majorVersion}.{changelog.minorVersion}.{changelog.patchVersion}
							</Badge>
							<span className="text-muted-foreground text-sm whitespace-nowrap">{format(changelog.publishedAt, "MMM d, yyyy")}</span>
						</div>
						<div>
							<h2 className="text-foreground/90 mb-3 text-lg leading-tight font-medium md:text-xl">{changelog.title}</h2>
							{/* <p className="text-sm text-muted-foreground md:text-base">{entry.description}</p> */}
							<div className="w-full">
								<div
									className="prose-headings:font-title font-default prose prose-zinc dark:prose-invert max-w-full focus:outline-hidden"
									dangerouslySetInnerHTML={{ __html: changelog.html ?? "" }}
								/>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
