import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { buttonVariants } from "@/components/ui/button";
import { ArrowRightIcon } from "lucide-react";
import { OSS_URL_HOST } from "@/lib/constants";
import { ImageModelLogo } from "@/config/image-models-config";

type ModelsShowType = {
	title: string;
	description?: string;
	logo: string;
	models: {
		name: string;
		description: string;
		url: string;
	}[];
}[];

function ModelsShow({ modelsShow, title, description }: { modelsShow: ModelsShowType; title: string; description?: string }) {
	return (
		<div className="py-24">
			<div className="container flex flex-col items-center justify-center gap-12 px-6">
				<div className="max-w-4xl text-center">
					<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>
				<div className="bg-muted w-full rounded-xl">
					<Tabs defaultValue={modelsShow[0].title} className="flex w-full flex-col gap-0 md:flex-row">
						<TabsList className="h-full w-full rounded-none bg-transparent p-4 md:w-fit md:p-8">
							<div className="flex h-full w-full flex-col items-start justify-start gap-2">
								{modelsShow.map((modelShow, index) => (
									<TabsTrigger
										key={index}
										value={modelShow.title}
										className={cn(
											"flex w-full items-center justify-start gap-3 border-none px-4 py-2 text-base font-semibold transition duration-150 ease-in-out",
										)}
									>
										<img src={modelShow.logo} className="size-4" alt="" loading="lazy" />
										<h3 className="whitespace-nowrap">{modelShow.title}</h3>
									</TabsTrigger>
								))}
							</div>
						</TabsList>
						<div className="border-b md:border-r"></div>
						{modelsShow.map((modelShow, index) => (
							<TabsContent key={index} value={modelShow.title}>
								<div className="flex flex-col p-4 md:p-8">
									<div className="flex flex-col overflow-hidden">
										<p className="pb-4 text-2xl font-medium">{modelShow.title}</p>
										{modelShow.description && <p className="text-secondary-foreground">{modelShow.description}</p>}
									</div>
									<div className="mt-8 flex flex-wrap gap-5 sm:grid sm:grid-cols-2 lg:grid-cols-3">
										{modelShow.models.map((model, index) => (
											<div
												key={index}
												className="bg-input flex max-h-52 min-h-52 w-full flex-col items-end justify-between gap-2 rounded-lg border p-3 px-4"
											>
												<div className="flex w-full flex-col gap-2">
													<p className="line-clamp-1 font-medium">{model.name}</p>
													<p className="text-secondary-foreground line-clamp-5 text-sm">{model.description}</p>
												</div>
												<div className="">
													<a
														href={model.url}
														className={cn(
															buttonVariants({ size: "sm" }),
															"bg-secondary-foreground hover:bg-secondary-foreground/80 before:content-(--content)",
														)}
														style={{ "--content": "'Try Now'" } as React.CSSProperties}
													>
														<ArrowRightIcon />
													</a>
												</div>
											</div>
										))}
									</div>
								</div>
							</TabsContent>
						))}
					</Tabs>
				</div>
			</div>
		</div>
	);
}

export function ImageModelsShow({ title, description }: { title: string; description?: string }) {
	const modelsShow: ModelsShowType = [
		{
			title: "Google Imagen",
			description:
				"Google Imagen is a series of advanced text-to-image diffusion models developed by Google DeepMind, designed to generate photorealistic and highly detailed images from natural language prompts.",
			logo: ImageModelLogo.Imagen,
			models: [
				{
					name: "Google Imagen 4",
					description:
						"A cutting-edge text-to-image model that generates hyper-detailed, photorealistic images with exceptional clarity and 2K resolution.",
					url: "/ai-image-generator?model=imagen-4-preview",
				},
				{
					name: "Google Imagen 4 Fast",
					description: "A speed-optimized variant of Imagen 4, capable of producing images up to 10 times faster, ideal for rapid iterations.",
					url: "/ai-image-generator?model=imagen-4-preview-fast",
				},
				{
					name: "Google Imagen 4 Ultra",
					description:
						"The most advanced version, combining unmatched precision, clarity, and text fidelity for professional-grade image generation. The most advanced versi .",
					url: "/ai-image-generator?model=imagen-4-preview-ultra",
				},
			],
		},
		{
			title: "Flux",
			description:
				"Flux is a groundbreaking AI model that transforms text into stunning, highly detailed images, empowering creativity with speed, precision, and endless possibilities.",
			logo: ImageModelLogo.Flux,
			models: [
				{
					name: "Flux.1 Fast",
					description: "A lightning-quick model optimized for speed, generating images in just 1-4 steps with slightly reduced quality.",
					url: "/ai-image-generator?model=flux-1-fast",
				},
				{
					name: "Flux.1 Dev",
					description: "A versatile, open-source model designed for development and experimentation, balancing efficiency and quality.",
					url: "/ai-image-generator?model=flux-1-dev",
				},
				{
					name: "Flux 1.1 Pro",
					description: "A refined model offering superior speed, improved image quality, and better prompt adherence for professional-grade results.",
					url: "/ai-image-generator?model=flux-1-1-pro",
				},
				{
					name: "Flux 1.1 Pro Ultra",
					description: "The ultimate version, delivering ultra-high-resolution images with exceptional detail and rapid generation times.",
					url: "/ai-image-generator?model=flux-1-1-pro-ultra",
				},
				{
					name: "Flux.1 Kontext Dev",
					description:
						"An open-source model designed for iterative image editing, focusing on character consistency and efficient local modifications based on text instructions.",
					url: "/image-to-image?model=flux-1-kontext-dev",
				},
				{
					name: "Flux.1 Kontext Pro",
					description:
						"A professional-grade model that excels in intelligent image editing and generation, allowing for precise modifications using both text and reference images.",
					url: "/ai-image-generator?model=flux-1-kontext-pro",
				},
				{
					name: "Flux.1 Kontext Max",
					description:
						"The premium version that maximizes performance and prompt adherence, delivering high-quality image transformations with advanced typography handling.",
					url: "/ai-image-generator?model=flux-1-kontext-max",
				},
			],
		},
		{
			title: "Ideogram",
			description:
				"Ideogram is a generative model that generates high-quality, realistic visuals with accurate text rendering for graphic design and creative projects.",
			logo: ImageModelLogo.Ideogram,
			models: [
				{
					name: "Ideogram 3",
					description:
						"his latest Ideogram model delivers highly realistic images, precise typography, and consistent style control for professional graphic design and branding.",
					url: "/ai-image-generator?model=ideogram-3-balanced",
				},
			],
		},
		{
			title: "Recraft",
			description:
				"Recraft is a top-performing AI model that generates high-quality vector and raster images with accurate text and precise style control.",
			logo: ImageModelLogo.Recraft,
			models: [
				{
					name: "Recraft 3",
					description:
						"This latest version offers advanced features that enhance image creation and editing, making it easier for users to bring their creative visions to life.",
					url: "/ai-image-generator?model=recraft-3",
				},
			],
		},
		{
			title: "Seedream",
			description:
				"Seedream is ByteDance's advanced bilingual text-to-image model that generates high-resolution images with exceptional detail and accuracy.",
			logo: ImageModelLogo.Seedream,
			models: [
				{
					name: "Seedream 3",
					description:
						"The lastest Seedream model creates super realistic high-resolution images with accurate text in both English and Chinese, fast.",
					url: "/ai-image-generator?model=seedream-3",
				},
			],
		},
	];

	return <ModelsShow modelsShow={modelsShow} title={title} description={description} />;
}

export function VideoModelsShow({ title, description }: { title: string; description?: string }) {
	const modelsShow: ModelsShowType = [
		{
			title: "Flux",
			description: "Flux is a text-to-image model.",
			logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
			models: [
				{
					name: "Flux.1 Fast",
					description: "Flux.1 Fast is a fast and accurate text-to-image model.",
					url: "/ai-image-generator?model=flux-1-fast",
				},
				{
					name: "Flux.1 Dev",
					description: "Flux.1 Dev is a developer-friendly text-to-image model.",
					url: "/ai-image-generator?model=flux-1-dev",
				},
			],
		},
		{
			title: "Google Imagen",
			logo: `${OSS_URL_HOST}icon/model/google-dark.webp`,
			models: [
				{
					name: "Imagen 4",
					description: "Imagen 4 is the latest version of the Imagen model.",
					url: "/ai-image-generator?model=imagen-4-preview",
				},
			],
		},
	];

	return <ModelsShow modelsShow={modelsShow} title={title} description={description} />;
}
