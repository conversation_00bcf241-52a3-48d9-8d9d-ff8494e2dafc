import { Example } from "@/components/landing/example";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import ImageGenerator from "../_components/image-ai.client";

export const metadata: Metadata = {
	title: `Image to Image | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/image-to-image",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="">
				<ImageGenerator imageToImage={true} />
			</div>

			<section>
				<div className="relative pt-20 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">Image to Image</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create AI images from Dreampik AI for free✨✨</div>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
