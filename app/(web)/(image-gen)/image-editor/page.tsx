import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import FeaturesComponent from "@/components/landing/features";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";
import ImageEditor from "./image-editor.client";

export const metadata: Metadata = {
	title: `AI Image Editor | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/image-editor",
	},
};

export default async function Page() {
	return (
		<main className="">
			<div className="">
				<ImageEditor />
			</div>

			<section>
				<div className="relative space-y-20 pt-24 pb-20">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">AI Image Editor</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg"></div>
						</div>
						{/* <div className="mt-12 flex flex-col items-center justify-center gap-2 md:flex-row">
							<Link
								href="#"
								className={cn(
									buttonVariants({ size: "lg", variant: "secondary" }),
									`h-12 rounded-full bg-indigo-500 text-base text-nowrap hover:bg-indigo-500/80`,
								)}
							>
								Try Our AI Image Editor
							</Link>
						</div> */}
					</div>
				</div>
			</section>
		</main>
	);
}
