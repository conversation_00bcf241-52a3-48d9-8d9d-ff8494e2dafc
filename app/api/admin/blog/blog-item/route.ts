import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { blogItemSchema } from "@/server/db/schema.server";
import { getKVKeyBlog, getKVKeyBlogHeads } from "@/lib/utils";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { BlogSchemaReqType, blogTypeReqSchema } from "@/@types/admin/blog/blog";
import { deleteValue } from "@/server/kv/redis-upstash.server";

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const params: BlogSchemaReqType = await req.json();
	// console.log("params:", params);
	try {
		blogTypeReqSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	// return NextResponse.json({ status: 500, message: "ok" });

	let updateData: any = {
		title: params.title,
		lang: params.lang,
		intro: params.intro,
		html: params.html,
		status: params.status,
		publishedAt: new Date(params.publishedAt),
	};
	if (params.categoryId) {
		updateData.categoryId = params.categoryId;
	}
	if (params.metaTitle) {
		updateData.metaTitle = params.metaTitle;
	}
	if (params.metaDescription) {
		updateData.metaDescription = params.metaDescription;
	}
	// if (params.image) {
	// 	updateData.image = params.image;
	// }
	// if (params.intro) {
	// 	updateData.intro = params.intro;
	// }
	// if (params.html) {
	// 	updateData.html = params.html;
	// }
	// if (params.publishedAt) {
	// 	updateData.publishedAt = params.publishedAt;
	// }
	if (params.status) {
		updateData.status = params.status;
	}

	const currentDate = new Date();

	let blogs;

	const db = getDB();
	if (params.id) {
		// update
		blogs = await db
			.update(blogItemSchema)
			.set({
				...updateData,
				updatedAt: currentDate,
			})
			.where(eq(blogItemSchema.id, params.id))
			.returning({
				id: blogItemSchema.id,
			});

		// delete blog item kv cache
		await deleteValue(getKVKeyBlog(params.lang, params.slug));
	} else {
		// insert
		blogs = await db
			.insert(blogItemSchema)
			.values({
				...updateData,
				slug: params.slug,
			})
			.onConflictDoNothing({
				target: blogItemSchema.slug,
			})
			.returning({
				id: blogItemSchema.id,
			});
	}

	// delete blog heads kv cache
	await deleteValue(getKVKeyBlogHeads(1));

	if (blogs.length > 0) {
		return NextResponse.json({ status: 200, message: "ok", newBlogId: blogs[0].id });
	}
	return NextResponse.json({ status: 200, message: "ok" });
}
