import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { desc } from "drizzle-orm";
import { blogCategorySchema } from "@/server/db/schema.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { BlogCategorySchemaType, blogCategoryTypeSchema } from "@/@types/admin/blog/blog";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { KV_KEY_BLOG_CATEGORIES } from "@/lib/utils";

export async function GET(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const db = getDB();
	const categories = (await db.select().from(blogCategorySchema).orderBy(desc(blogCategorySchema.id))) as BlogCategorySchemaType[];

	return NextResponse.json({ status: 200, message: "ok", categories: categories });
}

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const params: BlogCategorySchemaType = await req.json();
	try {
		blogCategoryTypeSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	const updateData: any = {
		name: params.name,
		slug: params.slug,
	};

	const db = getDB();
	if (params.id) {
		// update
		const currentDate = new Date();
		await db
			.update(blogCategorySchema)
			.set({
				...updateData,
				updatedAt: currentDate.getTime(),
				updatedAtString: currentDate.toISOString(),
			})
			.where(eq(blogCategorySchema.id, params.id));
	} else {
		// insert
		await db.insert(blogCategorySchema).values(updateData).onConflictDoNothing({
			target: blogCategorySchema.slug,
		});
	}

	// delete blog categories kv cache
	await deleteValue(KV_KEY_BLOG_CATEGORIES);
	return NextResponse.json({ status: 200, message: "ok" });
}

// export async function DELETE(req: Request) {
// 	// 1. Get user
// 	const sessionUser = await getCurrentSessionUser();
// 	if (!sessionUser || sessionUser.email !== "<EMAIL>") {
// 		return NextResponse.json({ status: 401, message: "Not authorized." });
// 	}

// 	const params: { id: number } = await req.json();
// 	if (!params.id) {
// 		return NextResponse.json({ status: 400, message: "The params is invalid." });
// 	}

// 	await db.delete(blogTagSchema).where(eq(blogTagSchema.id, params.id));
// 	return NextResponse.json({ status: 200, message: "ok" });
// }
