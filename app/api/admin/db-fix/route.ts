import { NextResponse } from "next/server";
// import { getDB } from "@/server/db/db-client.server";
// import { imageResultSchema } from "@/server/db/schema.server";

interface ImageResultData {
	uid: string;
	user_uid: string;
	prompt: string;
	filePath: string | null;
	file_origin_url: string;
	status: number;
	visibility: number;
	status_admin: number;
	credits_source: string | null;
	remark: string | null;
	created_at: number;
	updated_at: number;
	model: string;
	task_id: string | null;
}

export async function POST(req: Request) {
	return NextResponse.json("Not found.", { status: 404 });
	// try {
	// 	const imageDataArray: ImageResultData[] = await req.json();

	// 	if (!Array.isArray(imageDataArray)) {
	// 		return NextResponse.json({ status: 400, message: "Invalid data format. Expected an array." });
	// 	}

	// 	// Insert data into imageResultSchema
	// 	const insertResults = await db.insert(imageResultSchema).values(
	// 		imageDataArray.map((item) => ({
	// 			uid: item.uid,
	// 			userId: item.user_uid,
	// 			prompt: item.prompt,
	// 			filePath: item.filePath,
	// 			fileOriginUrl: item.file_origin_url,
	// 			status: item.status,
	// 			visibility: item.visibility === 1, // Convert to boolean
	// 			statusAdmin: item.status_admin,
	// 			creditsSources: item.credits_source,
	// 			remark: item.remark,
	// 			createdAt: new Date(item.created_at),
	// 			updatedAt: new Date(item.updated_at),
	// 			model: item.model,
	// 			taskId: item.task_id,
	// 		})),
	// 	);

	// 	return NextResponse.json({
	// 		status: 200,
	// 		message: "Data inserted successfully",
	// 		count: imageDataArray.length,
	// 	});
	// } catch (error: any) {
	// 	console.error("Error inserting data:", error);
	// 	return NextResponse.json({ status: 500, message: error.message });
	// }
}
