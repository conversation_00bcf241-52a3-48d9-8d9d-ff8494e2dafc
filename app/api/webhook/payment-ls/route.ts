import { CreditHistoryType } from "@/@types/membership-type";
import { WEBNAME } from "@/lib/constants";
import { getKVKeyUser } from "@/lib/utils";
import { getOrderProductInfo, OrderSource } from "@/lib/utils-membership";
import { getDB } from "@/server/db/db-client.server";
import { NewOrder, orderSchema, NewUserCreditsHistory, userCreditsHistorySchema, userSchema } from "@/server/db/schema.server";
import { notifyDevEvent, notifyPurchaseEvent } from "@/server/dev-notify.server";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { addMonths } from "date-fns";
import { eq, sql } from "drizzle-orm";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
	// console.log('webhook');

	// 1. validate x-signature
	const rawBody = await request.text();
	const sigString = request.headers.get("x-signature");
	const isSignatureValid = await verifySignature(rawBody, sigString!);
	if (!isSignatureValid) {
		return NextResponse.json({ message: "Invalid signature" }, { status: 403 });
	}

	// 2. handle webhook event
	const body = JSON.parse(rawBody);
	const eventName = body.meta.event_name;
	const userId = body.meta.custom_data.user_id;
	if (!userId) {
		throw new Error("No user ID, can't process");
	}
	try {
		const currentDate = new Date();

		const orderId = body.data.id as string;
		const attributes = body.data.attributes;

		if (eventName === "order_created") {
			// 0. Get data from invoice
			const status = attributes.status as string;

			const billedAtString = attributes.created_at as string;
			const billedAt = new Date(billedAtString);
			const refunded = attributes.refunded as boolean;
			const refundedAtString = attributes.refunded_at ? (attributes.refunded_at as string) : null;
			const refundedAt = refundedAtString ? new Date(refundedAtString) : null;
			const userEmail = attributes.user_name as string;
			const firstOrderItem = attributes.first_order_item;
			const productId = firstOrderItem.product_id as number;
			const productName = firstOrderItem.product_name as string;
			const variantId = firstOrderItem.variant_id as number;
			const variantName = firstOrderItem.variant_name as string;
			const subtotalFormatted = attributes.subtotal_formatted as string;

			// 1. check if order already exists and user order is initialized(billingReason is not null)
			const db = getDB();
			const orders = await db.select().from(orderSchema).where(eq(orderSchema.orderId, orderId));
			if (orders.length !== 0 && orders[0].billingReason) {
				return NextResponse.json("OK", { status: 200 });
			}

			// 2. get order info
			const orderCreditsInfo = getOrderProductInfo(variantId);
			const membership = orderCreditsInfo?.membership;
			const credits = orderCreditsInfo?.credits ?? 0;

			// 3. top up credits for order to user
			// 4. Insert credits change record to db: user_credits_history
			// 5. update order to db: order
			const insertUserCreditsHistoryData: NewUserCreditsHistory = {
				userId: userId,
				type: CreditHistoryType.Add,
				creditsOneTime: credits,
				remark: `onetime order: ${orderId}, productId: ${variantId}. billingReason: purchase.`,
			};
			const updateOrderData = {
				source: OrderSource.Lmsqueezy,
				status: status,
				billingReason: "purchase",

				productId: variantId.toString(),

				currency: "usd",

				createdAt: billedAt,
				updatedAt: currentDate,
			};
			await db.transaction(async (tx) => {
				await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
				await tx
					.update(userSchema)
					.set({
						// membershipId: membership?.id,
						// membershipFormatted: membership?.name,
						creditOneTime: sql`${userSchema.creditOneTime} + ${credits}`,
						creditOneTimeEndsAt: addMonths(billedAt, 1),
						updatedAt: currentDate,
					})
					.where(eq(userSchema.id, userId));
				await tx
					.insert(orderSchema)
					.values({
						userId: userId,
						orderId: orderId,
						...updateOrderData,
					} as NewOrder)
					.onConflictDoUpdate({
						target: orderSchema.orderId,
						set: {
							...updateOrderData,
						},
					});
			});

			await deleteValue(getKVKeyUser(userId));

			notifyPurchaseEvent(`${WEBNAME} - LemonSqueezy`, `New order: ${orderId}. Email: ${userEmail}`, `${subtotalFormatted}`, null);
		}
		if (eventName === "order_refunded") {
			const userEmail = attributes.user_name as string;
			notifyPurchaseEvent(
				`${WEBNAME} - LemonSqueezy Order Refund`,
				"Order Refund",
				`Order: ${orderId}, userId: ${userId}, user email: ${userEmail}`,
				null,
			);
		}

		return NextResponse.json("OK", { status: 200 });
	} catch (error: any) {
		console.log("error");
		notifyDevEvent(`${WEBNAME} - /api/webhook/payment-ls`, "Error", error.message, null);
		return NextResponse.json(error.message, { status: 500 });
	}
}

function checkCustomerExternalId(userId: string | null | undefined) {
	if (!userId) {
		throw new Error("No user ID was provided.");
	}
}

// lemonsqueezy
async function verifySignature(rawBody: any, sigString: string): Promise<boolean> {
	// const sigString = request.headers.get("x-signature");
	// const rawBody = await request.text();
	const secret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;

	const matches = sigString.match(/[\da-f]{2}/gi) ?? [];
	const signatureBytes = new Uint8Array(matches.map((h) => parseInt(h, 16)));

	const encoder = new TextEncoder();
	const secretData = encoder.encode(secret);
	const rawBodyData = encoder.encode(rawBody);

	const key = await crypto.subtle.importKey("raw", secretData, { name: "HMAC", hash: "SHA-256" }, false, ["verify"]);

	const verified = await crypto.subtle.verify("HMAC", key, signatureBytes, rawBodyData);
	// console.log("verified: ", verified)
	// return true
	return verified;
}
