import { getAuth } from "@/server/auth/better-auth";
import { toNextJsHandler } from "better-auth/next-js";
import type { NextRequest } from "next/server";

// export const { POST, GET } = toNextJsHandler(auth);

export async function GET(request: NextRequest) {
	const auth = getAuth();
	const handlers = toNextJsHandler(auth);
	return handlers.GET(request);
}

export async function POST(request: NextRequest) {
	const auth = getAuth();
	const handlers = toNextJsHandler(auth);
	return handlers.POST(request);
}

// import { NextResponse } from "next/server";
// export async function GET(request: Request) {
// 	return NextResponse.json({ status: 400, message: "Parameters is missing." });
// }
