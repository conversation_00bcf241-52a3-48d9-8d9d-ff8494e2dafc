import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBNAME } from "@/lib/constants";
import { Polar } from "@polar-sh/sdk";

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});
		const result = await polar.customerSessions.create({
			customerExternalId: sessionUser.id,
		});

		return NextResponse.json({ status: 200, url: result.customerPortalUrl });
	} catch (error: any) {
		console.log(error);
		notifyDevEvent(`${WEBNAME} - api: /api/payment/portal`, "Error", error.message, null);
		return NextResponse.json({ status: 500, message: error.message });
	}
}
