import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBNAME } from "@/lib/constants";
import { Polar } from "@polar-sh/sdk";
// import { resetUserSubscriptionResume } from "@/server/utils-user.server";
// import { SubscriptionCancel } from "@polar-sh/sdk/models/components/subscriptioncancel";
import { SubscriptionUpdateProduct } from "@polar-sh/sdk/models/components/subscriptionupdateproduct";
import { Subscription, subscriptionSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { eq, and } from "drizzle-orm";
import { canChangePlan } from "@/lib/utils-membership";

interface Params {
	subscriptionId?: string; // For subscripiton
	productId?: string;
	type: "resume" | "change";
}

export async function POST(req: Request) {
	const params: Params = await req.json();
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});

		if (params.type === "resume") {
			// Suspend usage due to API error.
			if (!params.subscriptionId) {
				return NextResponse.json({ status: 400, message: "subscriptionId is required." });
			}
			//TODO: this request got error: Use either `revoke` for immediate cancellation and revokation or `cancel_at_period_end
			// const result = await polar.subscriptions.update({
			// 	id: params.subscriptionId,
			// 	subscriptionUpdate: {
			// 		cancelAtPeriodEnd: false,
			// 	} as SubscriptionCancel,
			// });
			// console.log("result:", result);
			// await resetUserSubscriptionResume(sessionUser.id);
			// return NextResponse.json({ status: 200, message: "success" });
		}
		if (params.type === "change") {
			if (!params.subscriptionId) {
				return NextResponse.json({ status: 400, message: "subscriptionId is required." });
			}
			if (!params.productId) {
				return NextResponse.json({ status: 400, message: "productId is required." });
			}
			const db = getDB();
			const [userSubscription]: Subscription[] = await db
				.select()
				.from(subscriptionSchema)
				.where(and(eq(subscriptionSchema.subscriptionId, params.subscriptionId), eq(subscriptionSchema.userId, sessionUser.id)));
			if (!userSubscription) {
				return NextResponse.json({ status: 400, message: "No subscription found." });
			}
			if (!canChangePlan(userSubscription.productId, params.productId)) {
				return NextResponse.json({ status: 500, message: "Cannot change plan." });
			}

			const result = await polar.subscriptions.update({
				id: params.subscriptionId,
				subscriptionUpdate: {
					productId: params.productId,
					prorationBehavior: "invoice",
				} as SubscriptionUpdateProduct,
			});
			console.log("result:", result);
			return NextResponse.json({ status: 200, message: "success" });
		}

		return NextResponse.json({ status: 500 });
	} catch (error: any) {
		console.log(error);
		notifyDevEvent(`${WEBNAME} - api: /api/payment/subscription`, "Error", error.message, null);
		return NextResponse.json({ status: 500, message: error.message });
	}
}
