import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBNAME } from "@/lib/constants";
import { Polar } from "@polar-sh/sdk";
import { Checkout } from "@polar-sh/sdk/models/components/checkout";
import { CheckoutStatus } from "@polar-sh/sdk/models/components/checkoutstatus";

interface Params {
	checkoutId: string;
}

export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.checkoutId) {
		return NextResponse.json({ status: 400, message: "No checkout ID was provided." });
	}

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});

		// 获取checkout状态
		const checkout: Checkout = await polar.checkouts.get({
			id: params.checkoutId,
		});

		if (checkout.status === CheckoutStatus.Succeeded) {
			return NextResponse.json({ status: 200, checkoutStatus: "succeeded" });
		}
		if (checkout.status === CheckoutStatus.Confirmed) {
			return NextResponse.json({ status: 200, checkoutStatus: "pending" });
		}
		return NextResponse.json({ status: 200, checkoutStatus: "failed" });
	} catch (error: any) {
		notifyDevEvent(`${WEBNAME} - api: /api/payment/checkout/status`, "Error", error.message, null);
		return NextResponse.json({ status: 500, message: error.message });
	}
}
