import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { WEBNAME } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { falClient } from "@/server/ai/fal-config.server";
import { EVENT_GEN_IMAGE_TOOL } from "@/lib/track-events";
import { QueueStatus } from "@fal-ai/client";
import { getUserRealtime } from "@/server/utils-user.server";
import { userHasPaid } from "@/lib/utils-user";

type Params = {
	prompt: string;
	style: string;
	cameraStyle: string;
	cameraDirection: string;
	pacing: string;
	specialEffect: string;
	promtLength: string;
	image?: string;
};

export async function POST(req: Request) {
	const params: Params = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
	}
	if (!params.prompt) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const user = await getUserRealtime(userId);
		const isHasPaid = userHasPaid(user!.membershipId, user!.creditOneTimeEndsAt);
		let creditConsumes = null;
		if (!isHasPaid) {
			const { creditConsumes: consumes } = await checkUserCredit(userId, {
				needCredits: 2,
				existUser: user!,
			});
			creditConsumes = consumes;
		}

		let payload: any = {
			input_concept: params.prompt,
			style: params.style,
			camera_style: params.cameraStyle,
			camera_direction: params.cameraDirection,
			pacing: params.pacing,
			special_effects: params.specialEffect,
			// model: "openai/gpt-4o",
			model: "google/gemini-2.0-flash-001",
			prompt_length: params.promtLength,
		};
		if (params.image) {
			payload.image_url = params.image;
		}
		if (process.env.NODE_ENV === "development") {
			console.log("creditConsumes: ", creditConsumes);
			console.log("payload: ", payload);
		}

		// track mixpanel event
		// mixpanelTrackEvent(EVENT_GEN_IMAGE_TOOL, userId, {
		// 	mp_country_code: cfIpCountryCode,
		// 	free: visibility,
		// 	tool: "upscale-image",
		// });

		const falAIEndPoint = "fal-ai/video-prompt-generator";

		const { request_id: requestId } = await falClient.queue.submit(falAIEndPoint, {
			input: payload,
		});

		let resultPrompt: string | null = null;
		await new Promise((resolve) => setTimeout(resolve, 3000));
		while (true) {
			const { status }: QueueStatus = await falClient.queue.status(falAIEndPoint, {
				requestId: requestId,
			});
			// status: IN_QUEUE, IN_PROGRESS, COMPLETED
			if (status === "COMPLETED") {
				const result = await falClient.queue.result(falAIEndPoint, {
					requestId: requestId,
				});
				resultPrompt = result.data.prompt;
				break;
			} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
			} else {
				throw new Error(`Failed to generate prompt.`);
			}

			await new Promise((resolve) => setTimeout(resolve, 1000));
		}

		if (!resultPrompt) {
			throw new Error("Failed to generate prompt.");
		}

		// 更新用户credits
		if (creditConsumes) {
			await updateUserCredit(userId, creditConsumes, {
				remark: `Generate video prompt. Fal.ai request id: ${requestId}.`,
			});
		}

		return NextResponse.json({ message: "Success", result: resultPrompt });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/video/tool/prompt-generator`);
	}
}
