import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { handleApiError } from "@/@types/error-api";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { getDB } from "@/server/db/db-client.server";
import { and, desc, eq, gt } from "drizzle-orm";
import { mediaItemSchema } from "@/server/db/schema.server";
import { subDays } from "date-fns";

export async function POST(req: Request) {
	try {
		const userId = await getSessionUserId();

		const db = getDB();
		const mediaResults = await db
			.select({ uid: mediaItemSchema.uid, filePath: mediaItemSchema.mediaPaths, type: mediaItemSchema.type })
			.from(mediaItemSchema)
			.where(and(eq(mediaItemSchema.userId, userId), gt(mediaItemSchema.createdAt, subDays(new Date(), 30))))
			.orderBy(desc(mediaItemSchema.id));
		const mediaItems = mediaResults
			.filter((result) => result.filePath)
			.map((result) => ({
				uid: result.uid,
				url: `${OSS_URL_HOST}${result.filePath}`,
				type: result.type,
			}));

		return NextResponse.json({ mediaItems });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/user/my-creations`);
	}
}
