import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { UserInfoDB } from "@/@types/user";
import { refreshUser } from "@/server/refresh-user";

type Params = {
	type: string;
};

export async function POST(req: Request) {
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();

	if (params.type === "credits") {
		const userInfo: UserInfoDB | null = await refreshUser(userId);
		return NextResponse.json({ message: "Success", userInfo });
	}
	return NextResponse.json({ status: 400, message: "Parameters is missing." });
}
