import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { falGenImages } from "@/server/ai/fal-config.server";
import { PhotoStyleID } from "@/lib/utils-photo-effects";
import { EVENT_GEN_PHOTO_EFFECT } from "@/lib/track-events";

type Params = {
	image: string;
	style: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const params: Params = await req.json();
	if (!params.image || !params.style) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();
		// const { prompt, loraUrl, id: styleId } = getPhotoEffectLoraAndPrompt(params.style);

		const needCredits = 12;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		const falAIEndPoint = "fal-ai/image-editing/baby-version";
		const payload: any = {
			image_url: params.image,
		};
		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
			console.log("payload: ", payload);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_GEN_PHOTO_EFFECT, userId, {
			mp_country_code: cfIpCountryCode,
			free: visibility,
			style: PhotoStyleID.BabyFilter,
		});

		const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 7000, 1500);

		// save to r2
		const imagePath = await saveToR2(resultUrls[0]);

		// save to db
		const imageResultId = getUUIDString();
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					tool: MediaHeadToolType.PhotoEffect,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrls[0],
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: `${OSS_URL_HOST}${imagePath}` });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/photo-effect/baby-filter`);
	}
}
