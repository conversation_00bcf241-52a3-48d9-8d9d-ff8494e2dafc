import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { falGenImage } from "@/server/ai/fal-config.server";
import { EVENT_GEN_IMAGE_TOOL } from "@/lib/track-events";

type Params = {
	image: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const params: Params = await req.json();
	if (!params.image) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const needCredits = 3;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		const payload: any = {
			image_url: params.image,
		};
		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
			console.log("payload: ", payload);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_GEN_IMAGE_TOOL, userId, {
			mp_country_code: cfIpCountryCode,
			free: visibility,
			tool: "vectorize-image",
		});

		const falAIEndPoint = "fal-ai/recraft/vectorize";

		const resultUrl: string = await falGenImage(falAIEndPoint, payload, 10000, 2000);

		// save to r2
		const imagePath = await saveToR2(resultUrl, MediaHeadType.Image);

		// save to db
		const imageResultId = getUUIDString();
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					type: MediaHeadType.SVG,
					tool: MediaHeadToolType.ImageTool,
					model: "vectorize-image",
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				type: MediaHeadType.SVG,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: `${OSS_URL_HOST}${imagePath}` });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/tool/vectorize-image`);
	}
}
