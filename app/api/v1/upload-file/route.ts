import { NextResponse } from "next/server";
import { OSS_URL_HOST } from "@/lib/constants";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { checkIp } from "@/lib/utils-ip";
import { getCurrentMonthAndDayString } from "@/lib/utils-date";
import { createR2Url } from "@/server/r2.server";
import { getUUIDString } from "@/lib/utils";

interface Params {
	fileExtension: string | undefined | null;
	contentType: string | undefined | null;
}
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.fileExtension || !params.contentType) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	if (!checkIp(req)) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const fileId = getUUIDString();
	const filename = fileId + "." + params.fileExtension;
	const { yearMonth, yearMonthDay } = getCurrentMonthAndDayString();
	let url_suffix = `source/${yearMonth}/${yearMonthDay}-${filename}`;
	if (process.env.NODE_ENV === "development") {
		url_suffix = `dev/source/${yearMonth}/${yearMonthDay}-${filename}`;
	}

	try {
		const signedUrl = await createR2Url(url_suffix, params.contentType as string);
		const fileUrl = `${OSS_URL_HOST}${url_suffix}`;

		if (process.env.NODE_ENV === "development") {
			console.log("fileUrl:", fileUrl);
		}
		return NextResponse.json({ status: 200, url: signedUrl, method: "PUT", file_url: fileUrl });
	} catch (error: any) {
		return NextResponse.json({ status: 500, message: error.message || "Failed to upload file." });
	}
}
