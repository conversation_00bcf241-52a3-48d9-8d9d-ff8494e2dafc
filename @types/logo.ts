import { z } from "zod";

export interface LogoHead {
	id: number;
	uid: string;
	prompt: string;
	logoPath: string | null;
	label: number;
	vectorized: boolean;
	createdAt: Date;
}

export const soundPublicHeadSchema = z.object({
	uid: z.string().nonempty(),
	prompt: z.string().nonempty(),
	durationStart: z.number().min(0),
	durationMax: z.number().min(0),
	soundPath: z.string().nullable(),
	// createdAt: z.string().datetime(),
	createdAt: z.date(),
});
export type SoundPublicHead = z.infer<typeof soundPublicHeadSchema>;
export const soundPublicHeadArraySchema = z.array(soundPublicHeadSchema);
