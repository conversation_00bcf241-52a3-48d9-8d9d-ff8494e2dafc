import { z } from "zod";

export enum BlogStatus {
	Draft = 0,
	Published = 1,
}

export const getBlogStatusText = (status: number) => {
	switch (status) {
		case BlogStatus.Draft:
			return "Draft";
		case BlogStatus.Published:
			return "Published";
		default:
			return "Unknown";
	}
};

// Blog category for admin
export const blogCategoryTypeSchema = z.object({
	id: z.number().int().optional(),
	slug: z.string().optional(),
	name: z.string().trim().nonempty({
		message: "Name is required",
	}),
});
export type BlogCategorySchemaType = z.infer<typeof blogCategoryTypeSchema>;

// Blog item for admin
export const blogTypeSchema = z.object({
	id: z.number().int().optional(),
	categoryId: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	slug: z.string().trim().nonempty({
		message: "Slug is required",
	}),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	metaTitle: z.string().optional(),
	metaDescription: z.string().optional(),
	image: z.string().optional(),
	intro: z.string().trim().nonempty({
		message: "Summary(Intro) is required",
	}),
	html: z.string().trim().nonempty({
		message: "Blog content is required",
	}),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type BlogSchemaType = z.infer<typeof blogTypeSchema>;

export const blogTypeReqSchema = z.object({
	id: z.number().int().optional(),
	categoryId: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	slug: z.string().trim().nonempty({
		message: "Slug is required",
	}),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	metaTitle: z.string().optional(),
	metaDescription: z.string().optional(),
	image: z.string().optional(),
	intro: z.string().trim().nonempty({
		message: "Summary(Intro) is required",
	}),
	html: z.string().trim().nonempty({
		message: "Blog content is required",
	}),
	publishedAt: z.string().transform((val) => {
		const date = new Date(val);
		if (isNaN(date.getTime())) {
			throw new Error("Invalid date format");
		}
		return date;
	}),
	status: z.number().int(),
});
export type BlogSchemaReqType = z.infer<typeof blogTypeSchema>;

// Blog Head
export const blogPublicHeadSchema = z.object({
	categoryId: z.number().int().nullable(),
	lang: z.string(),
	slug: z.string(),
	title: z.string(),
	image: z.string().nullable(),
	intro: z.string(),
	publishedAt: z.date(),
});
export type BlogPublicHead = z.infer<typeof blogPublicHeadSchema>;
export const blogPublicHeadArraySchema = z.array(blogPublicHeadSchema);

// Blog detail
export const blogDetailSchema = z.object({
	categoryId: z.number().int().nullable(),
	lang: z.string(),
	slug: z.string(),
	metaTitle: z.string().nullable(),
	metaDescription: z.string().nullable(),
	title: z.string(),
	image: z.string().nullable(),
	intro: z.string(),
	html: z.string().nullable(),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type BlogDetailType = z.infer<typeof blogDetailSchema>;
