import { z } from "zod";

export enum ChangelogStatus {
	Draft = 0,
	Published = 1,
}

export const getChangelogStatusText = (status: number) => {
	switch (status) {
		case ChangelogStatus.Draft:
			return "Draft";
		case ChangelogStatus.Published:
			return "Published";
		default:
			return "Unknown";
	}
};

// Blog item for admin
export const changelogTypeSchema = z.object({
	id: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	majorVersion: z.number().int(),
	minorVersion: z.number().int(),
	patchVersion: z.number().int(),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	image: z.string().optional(),
	html: z.string().trim().nonempty({
		message: "Changelog content is required",
	}),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type ChangelogSchemaType = z.infer<typeof changelogTypeSchema>;

export const changelogTypeReqSchema = z.object({
	id: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	majorVersion: z.number().int(),
	minorVersion: z.number().int(),
	patchVersion: z.number().int(),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	image: z.string().optional(),
	html: z.string().trim().nonempty({
		message: "Changelog content is required",
	}),
	publishedAt: z.string().transform((val) => {
		const date = new Date(val);
		if (isNaN(date.getTime())) {
			throw new Error("Invalid date format");
		}
		return date;
	}),
	status: z.number().int(),
});
export type ChangelogSchemaReqType = z.infer<typeof changelogTypeReqSchema>;

// Changelog Head
export const changelogHeadSchema = z.object({
	id: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	majorVersion: z.number().int().nullable(),
	minorVersion: z.number().int().nullable(),
	patchVersion: z.number().int().nullable(),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	image: z.string().nullable(),
	html: z.string().nullable(),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type ChangelogHead = z.infer<typeof changelogHeadSchema>;
