export interface Subscription {
	id: number;
	userId: string;

	subscriptionId: string;
	status: string;
	recurringInterval: string;

	productId: string;

	currentPeriodStartAt: Date;
	currentPeriodEndAt: Date | null;
	cancelAtPeriodEnd: boolean;
	canceledAt: Date | null;
	startAt: Date | null;
	endAt: Date | null;
	endedAt: Date | null;
}

export interface Order {
	id: number;
	userUid: string;
	orderId: string;
	status: string;
	statusFormatted: string;
	productId: number;
	productName: string | null;
	variantId: number;
	variantName: string | null;
	name: string | null;
	email: string | null;
	billedAt: number | null;
	billedAtString: string | null;
	refunded: boolean;
	refundedAt: number | null;
	refundedAtString: string | null;
}
