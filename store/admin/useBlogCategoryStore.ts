import { BlogCategorySchemaType } from "@/@types/admin/blog/blog";
import { ofetch } from "ofetch";
import { create } from "zustand";

type BlogCategoryStore = {
	categories: BlogCategorySchemaType[] | [];
	isLoaded: boolean;
	setCategories: (categories: BlogCategorySchemaType[] | null) => void;
	refreshCategories: () => Promise<void>;
};
export const useBlogCategoryStore = create<BlogCategoryStore>((set) => {
	return {
		categories: [],
		isLoaded: false,
		setCategories: (categories: BlogCategorySchemaType[] | null) => {
			set({
				categories: categories ?? [],
				isLoaded: true,
			});
		},
		refreshCategories: async () => {
			try {
				const { status, message, categories } = await ofetch("/api/admin/blog-category", {
					method: "GET",
				});
				set({ categories: categories });
			} catch (error) {
				console.error("Failed to get blog categories:", error);
			}
		},
	};
});
