This is a [Next.js](https://nextjs.org/) project.

## Getting Started

### [Upstash](https://upstash.com/docs/redis/sdks/ts/developing)

1. `docker-compose-upstash-redis.yml` file:

```yml
version: "3"
services:
    redis:
        image: redis
        ports:
            - "6379:6379"
    serverless-redis-http:
        ports:
            - "8079:80"
        image: hiett/serverless-redis-http:latest
        environment:
            SRH_MODE: env
            SRH_TOKEN: redis-dev-token
            SRH_CONNECTION_STRING: "redis://redis:6379"
```

2. RUN

```bash
docker compose -f "docker-compose-upstash-redis.yml" up -d
```

3. ENV

```env
UPSTASH_REDIS_REST_URL=http://localhost:8079
UPSTASH_REDIS_REST_TOKEN=redis-dev-token
```

### Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Docker

```bash
docker compose -f "compose.prod.yml" build
docker push ghcr.io/notlandingstudio/dreampik-next:TAG
# or
npm run docker:build
npm run docker:push --tag=1.0.0
```
