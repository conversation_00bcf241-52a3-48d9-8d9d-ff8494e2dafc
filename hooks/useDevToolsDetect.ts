import { useEffect, useState } from 'react';

// 自定义 Hook 检测开发者工具状态
export function useDevToolsDetect() {
	const [isDevToolsOpen, setIsDevToolsOpen] = useState(false);

	useEffect(() => {
		// console.log("useDevToolsDetect");
		const threshold = 160;

		const checkDevTools = () => {
			const widthThreshold = window.outerWidth - window.innerWidth > threshold;
			const heightThreshold = window.outerHeight - window.innerHeight > threshold;
			
			if (widthThreshold || heightThreshold) {
				setIsDevToolsOpen(true);
			} else {
				setIsDevToolsOpen(false);
			}
		};

		const interval = setInterval(checkDevTools, 1000);

		return () => clearInterval(interval);
	}, []);

	return isDevToolsOpen;
}