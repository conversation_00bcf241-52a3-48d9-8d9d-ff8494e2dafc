import { useState, useEffect } from "react";

export function useSubmitTimer(isActive: boolean) {
	const [seconds, setSeconds] = useState<string>("0.0");
	const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

	useEffect(() => {
		if (isActive) {
			const id = setInterval(() => {
				setSeconds((prevSeconds) => (Number(prevSeconds) + 0.1).toFixed(1));
			}, 100);
			setIntervalId(id);
		} else if (intervalId) {
			setSeconds("0.0");
			clearInterval(intervalId);
			setIntervalId(null);
		}

		return () => {
			if (intervalId) clearInterval(intervalId);
		};
	}, [isActive]);

	return { seconds };
}
