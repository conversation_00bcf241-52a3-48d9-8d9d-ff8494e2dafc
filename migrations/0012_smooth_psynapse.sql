CREATE TABLE `subscription` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`subscription_id` text NOT NULL,
	`source` text NOT NULL,
	`status` text NOT NULL,
	`recurring_interval` text NOT NULL,
	`product_id` text NOT NULL,
	`checkout_id` text,
	`current_period_start_at` integer NOT NULL,
	`current_period_end_at` integer,
	`cancel_at_period_end` integer DEFAULT false NOT NULL,
	`canceled_at` integer,
	`start_at` integer,
	`end_at` integer,
	`ended_at` integer,
	`customer_cancellation_reason` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `subscription_subscription_id_unique` ON `subscription` (`subscription_id`);--> statement-breakpoint
CREATE INDEX `idx_subscription_user_uid` ON `subscription` (`user_uid`);