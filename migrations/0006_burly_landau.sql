CREATE TABLE `media_task` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`type` text DEFAULT 'image' NOT NULL,
	`model` text,
	`model_name` text,
	`prompt` text,
	`video_duration` integer,
	`aspect_ratio` text,
	`seed` text,
	`request_id` text NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`visibility` integer DEFAULT true NOT NULL,
	`media_head_uid` text,
	`credits_source` text,
	`ip` text,
	`remark` text,
	`error` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `media_task_user_uid_index` ON `media_task` (`user_uid`);--> statement-breakpoint
CREATE INDEX `media_task_request_id_index` ON `media_task` (`request_id`);--> statement-breakpoint
ALTER TABLE `media_head` ADD `type` text DEFAULT 'image' NOT NULL;--> statement-breakpoint
ALTER TABLE `media_head` ADD `model_name` text;--> statement-breakpoint
ALTER TABLE `media_head` ADD `video_duration` integer;--> statement-breakpoint
ALTER TABLE `media_head` ADD `aspect_ratio` text;--> statement-breakpoint
ALTER TABLE `media_head` ADD `seed` text;--> statement-breakpoint
ALTER TABLE `media_head` ADD `visibility` integer DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE `media_head` ADD `task_id` integer;