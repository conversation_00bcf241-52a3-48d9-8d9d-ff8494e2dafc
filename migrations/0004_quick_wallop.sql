CREATE TABLE `media_head` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`model` text,
	`prompt` text,
	`status` integer DEFAULT 0 NOT NULL,
	`credits_source` text,
	`remark` text,
	`error_reason` text,
	`created_at` integer DEFAULT (unixepoch() * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch() * 1000) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `media_head_uid_unique` ON `media_head` (`uid`);--> statement-breakpoint
CREATE INDEX `media_head_user_uid_index` ON `media_head` (`user_uid`);--> statement-breakpoint
CREATE INDEX `media_head_status` ON `media_head` (`status`);--> statement-breakpoint
CREATE TABLE `media_item` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`media_head_uid` text NOT NULL,
	`type` text DEFAULT 'image' NOT NULL,
	`visibility` integer DEFAULT true NOT NULL,
	`media_paths` text,
	`media_origin_urls` text,
	`created_at` integer DEFAULT (unixepoch() * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch() * 1000) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `media_item_uid_unique` ON `media_item` (`uid`);--> statement-breakpoint
CREATE INDEX `media_item_media_head_uid_index` ON `media_item` (`media_head_uid`);--> statement-breakpoint
CREATE INDEX `media_item_visibility` ON `media_item` (`visibility`);