DROP TABLE `image_result`;--> statement-breakpoint
DROP TABLE `image_task`;--> statement-breakpoint
DROP INDEX "better_auth_account_uid_unique";--> statement-breakpoint
DROP INDEX "better_auth_account_user_uid_index";--> statement-breakpoint
DROP INDEX "better_auth_account_provider_unique";--> statement-breakpoint
DROP INDEX "blog_category_slug_unique";--> statement-breakpoint
DROP INDEX "blog_item_slug_unique";--> statement-breakpoint
DROP INDEX "blog_item_lang_index";--> statement-breakpoint
DROP INDEX "blog_item_category_index";--> statement-breakpoint
DROP INDEX "blog_item_status_index";--> statement-breakpoint
DROP INDEX "changelog_item_lang_index";--> statement-breakpoint
DROP INDEX "changelog_item_status_index";--> statement-breakpoint
DROP INDEX "media_head_uid_unique";--> statement-breakpoint
DROP INDEX "media_head_user_uid_index";--> statement-breakpoint
DROP INDEX "media_head_status";--> statement-breakpoint
DROP INDEX "idx_media_head_type";--> statement-breakpoint
DROP INDEX "media_item_uid_unique";--> statement-breakpoint
DROP INDEX "media_item_user_uid_index";--> statement-breakpoint
DROP INDEX "media_item_media_head_uid_index";--> statement-breakpoint
DROP INDEX "media_item_visibility";--> statement-breakpoint
DROP INDEX "idx_media_item_type";--> statement-breakpoint
DROP INDEX "media_task_user_uid_index";--> statement-breakpoint
DROP INDEX "media_task_request_id_index";--> statement-breakpoint
DROP INDEX "order_order_id_unique";--> statement-breakpoint
DROP INDEX "order_user_uid_index";--> statement-breakpoint
DROP INDEX "better_auth_session_uid_unique";--> statement-breakpoint
DROP INDEX "better_auth_session_token_index";--> statement-breakpoint
DROP INDEX "user_credits_history_user_uid_index";--> statement-breakpoint
DROP INDEX "user_uid_unique";--> statement-breakpoint
DROP INDEX "user_email_unique";--> statement-breakpoint
DROP INDEX "better_auth_verification_uid_unique";--> statement-breakpoint
DROP INDEX "better_auth_verification_identifier_index";--> statement-breakpoint
DROP INDEX "better_auth_verification_expires_at_index";--> statement-breakpoint
ALTER TABLE `better_auth_account` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_account_uid_unique` ON `better_auth_account` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_account_user_uid_index` ON `better_auth_account` (`user_uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_account_provider_unique` ON `better_auth_account` (`account_id`,`provider_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `blog_category_slug_unique` ON `blog_category` (`slug`);--> statement-breakpoint
CREATE UNIQUE INDEX `blog_item_slug_unique` ON `blog_item` (`slug`);--> statement-breakpoint
CREATE INDEX `blog_item_lang_index` ON `blog_item` (`lang`);--> statement-breakpoint
CREATE INDEX `blog_item_category_index` ON `blog_item` (`category_id`);--> statement-breakpoint
CREATE INDEX `blog_item_status_index` ON `blog_item` (`status`);--> statement-breakpoint
CREATE INDEX `changelog_item_lang_index` ON `changelog_item` (`lang`);--> statement-breakpoint
CREATE INDEX `changelog_item_status_index` ON `changelog_item` (`status`);--> statement-breakpoint
CREATE UNIQUE INDEX `media_head_uid_unique` ON `media_head` (`uid`);--> statement-breakpoint
CREATE INDEX `media_head_user_uid_index` ON `media_head` (`user_uid`);--> statement-breakpoint
CREATE INDEX `media_head_status` ON `media_head` (`status`);--> statement-breakpoint
CREATE INDEX `idx_media_head_type` ON `media_head` (`type`);--> statement-breakpoint
CREATE UNIQUE INDEX `media_item_uid_unique` ON `media_item` (`uid`);--> statement-breakpoint
CREATE INDEX `media_item_user_uid_index` ON `media_item` (`user_uid`);--> statement-breakpoint
CREATE INDEX `media_item_media_head_uid_index` ON `media_item` (`media_head_uid`);--> statement-breakpoint
CREATE INDEX `media_item_visibility` ON `media_item` (`visibility`);--> statement-breakpoint
CREATE INDEX `idx_media_item_type` ON `media_item` (`type`);--> statement-breakpoint
CREATE INDEX `media_task_user_uid_index` ON `media_task` (`user_uid`);--> statement-breakpoint
CREATE INDEX `media_task_request_id_index` ON `media_task` (`request_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `order_order_id_unique` ON `order` (`order_id`);--> statement-breakpoint
CREATE INDEX `order_user_uid_index` ON `order` (`user_uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_session_uid_unique` ON `better_auth_session` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_session_token_index` ON `better_auth_session` (`token`);--> statement-breakpoint
CREATE INDEX `user_credits_history_user_uid_index` ON `user_credits_history` (`user_uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `user_uid_unique` ON `user` (`uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `user_email_unique` ON `user` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_verification_uid_unique` ON `better_auth_verification` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_verification_identifier_index` ON `better_auth_verification` (`identifier`);--> statement-breakpoint
CREATE INDEX `better_auth_verification_expires_at_index` ON `better_auth_verification` (`expires_at`);--> statement-breakpoint
ALTER TABLE `better_auth_account` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `blog_category` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `blog_category` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `blog_item` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `blog_item` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `changelog_item` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `changelog_item` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `media_head` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `media_head` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `media_item` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `media_item` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `order` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `order` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `better_auth_session` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `better_auth_session` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `user_credits_history` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `user_credits_history` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `user` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `user` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `user` ADD `ip` text;--> statement-breakpoint
CREATE INDEX `idx_user_ip` ON `user` (`ip`);--> statement-breakpoint
ALTER TABLE `user` ADD `remark` text;--> statement-breakpoint
ALTER TABLE `better_auth_verification` ALTER COLUMN "created_at" TO "created_at" integer NOT NULL;--> statement-breakpoint
ALTER TABLE `better_auth_verification` ALTER COLUMN "updated_at" TO "updated_at" integer NOT NULL;