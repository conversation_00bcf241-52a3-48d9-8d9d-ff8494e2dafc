.tiptap-dropdown-menu {
  --tt-popover-bg-color: var(--white);
  --tt-popover-border-color: var(--tt-gray-light-a-100);
  --tt-popover-text-color: var(--tt-gray-light-a-600);
  --tt-popover-label: var(--tt-gray-light-a-400);

  .dark & {
    --tt-popover-border-color: var(--tt-gray-dark-a-50);
    --tt-popover-bg-color: var(--tt-gray-dark-50);
    --tt-popover-text-color: var(--tt-gray-dark-a-600);
    --tt-popover-label: var(--tt-gray-dark-a-400);
  }
}

/* --------------------------------------------
      --------- POPOVER STYLING SETTINGS -----------
      -------------------------------------------- */
.tiptap-dropdown-menu {
  --padding: 0.25rem;
  --border-width: 1px;

  z-index: 200;
  border-radius: calc(
    var(--padding) + var(--tt-radius-lg) + var(--border-width)
  );
  border: var(--border-width) solid var(--tt-popover-border-color);
  background-color: var(--tt-popover-bg-color);
  padding: var(--padding);
  color: var(--tt-popover-text-color);
  box-shadow: var(--tt-shadow-elevated-md);
  outline: none;
  overflow: auto;
  gap: 0.25rem;

  button {
    width: 100%;
  }

  .tiptap-dropdown-menu-separator {
    margin: 0.25rem 0;
  }

  .tiptap-dropdown-menu-label {
    margin-left: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--tt-popover-label);
  }

  /* Animation states */
  &[data-state="open"] {
    animation:
      fadeIn 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-state="closed"] {
    animation:
      fadeOut 150ms cubic-bezier(0.16, 1, 0.3, 1),
      zoomOut 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Position-based animations */
  &[data-side="top"],
  &[data-side="top-start"],
  &[data-side="top-end"] {
    animation: slideFromBottom 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="right"],
  &[data-side="right-start"],
  &[data-side="right-end"] {
    animation: slideFromLeft 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="bottom"],
  &[data-side="bottom-start"],
  &[data-side="bottom-end"] {
    animation: slideFromTop 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  &[data-side="left"],
  &[data-side="left-start"],
  &[data-side="left-end"] {
    animation: slideFromRight 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
}
