"use client";

import React, { useState } from "react";
import SimpleMarquee from "@/fancy/components/blocks/simple-marquee";
import { cn } from "@/lib/utils";
import { TerminalIcon } from "lucide-react";
import { useCopyToClipboard } from "usehooks-ts";
import { toast } from "sonner";

const MarqueeItem = ({ children, className }: { children: React.ReactNode; className?: string }) => (
	<div className={cn("mx-2 cursor-pointer duration-300 ease-in-out hover:scale-105 sm:mx-3 md:mx-4", className)}>{children}</div>
);

export function ImageMarqueeShow({
	images,
}: {
	images: {
		url: string;
		prompt?: string;
	}[];
}) {
	const n = images.length;

	// 第一个切分点向上取整，确保第一部分能获得余数
	const firstSplit = Math.ceil(n / 3);

	// 第二个切分点同样向上取整
	const secondSplit = Math.ceil((2 * n) / 3);

	const firstThird = images.slice(0, firstSplit);
	const secondThird = images.slice(firstSplit, secondSplit);
	const lastThird = images.slice(secondSplit);

	// const firstThird = images.slice(0, Math.floor(images.length / 3));
	// const secondThird = images.slice(Math.floor(images.length / 3), Math.floor((2 * images.length) / 3));
	// const lastThird = images.slice(Math.floor((2 * images.length) / 3));

	const [container, setContainer] = useState<HTMLElement | null>(null);

	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text?: string) => () => {
		if (!text) return;
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error) => {
				toast.error("Failed to copy!", error);
			});
	};

	return (
		<div
			className="relative flex h-[304px] flex-col items-center justify-center overflow-auto overflow-x-hidden sm:h-[384px] md:h-[572px]"
			ref={(node) => setContainer(node)}
		>
			<div className="absolute top-0 flex w-full flex-col items-center justify-center space-y-2 sm:space-y-3 md:space-y-4">
				<SimpleMarquee
					className="w-full"
					baseVelocity={4}
					repeat={4}
					draggable={false}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowDownFactor={0.1}
					slowdownOnHover
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					scrollAwareDirection={true}
					scrollContainer={{ current: container! }}
					useScrollVelocity={true}
					direction="left"
				>
					{firstThird.map((image, i) => (
						<MarqueeItem key={i}>
							<div className="group relative cursor-pointer overflow-hidden" onClick={onCopy(image.prompt)}>
								<img src={image.url} alt={`Image ${i + 1}`} className="aspect-[3/2] h-24 object-cover sm:h-[120px] md:h-[180px]" />
								{image.prompt && (
									<div className="absolute inset-0 bg-black/50 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
										<p
											className={cn(
												"absolute top-2 right-2 left-2 hidden rounded-sm px-2 py-1 text-[12px] font-[350] text-zinc-100 transition-all duration-300 group-hover:block after:content-(--content)",
												"group-hover:line-clamp-2 sm:group-hover:line-clamp-3",
											)}
											style={{ "--content": `'${image.prompt}'` } as React.CSSProperties}
										>
											<TerminalIcon className="size-4" />
										</p>
									</div>
								)}
							</div>
						</MarqueeItem>
					))}
				</SimpleMarquee>

				<SimpleMarquee
					className="w-full"
					baseVelocity={3}
					repeat={4}
					scrollAwareDirection={true}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowdownOnHover
					slowDownFactor={0.1}
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					useScrollVelocity={true}
					scrollContainer={{ current: container! }}
					draggable={false}
					direction="right"
				>
					{secondThird.map((image, i) => (
						<MarqueeItem key={i}>
							<div className="group relative cursor-pointer overflow-hidden" onClick={onCopy(image.prompt)}>
								<img
									src={image.url}
									alt={`Image ${i + firstThird.length}`}
									className="aspect-[3/2] h-24 object-cover sm:h-[120px] md:h-[180px]"
								/>
								{image.prompt && (
									<div className="absolute inset-0 bg-black/50 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
										<p
											className={cn(
												"absolute top-2 right-2 left-2 hidden rounded-sm px-2 py-1 text-[14px] font-[350] text-zinc-100 transition-all duration-300 group-hover:block after:content-(--content)",
												"group-hover:line-clamp-2 sm:group-hover:line-clamp-3",
											)}
											style={{ "--content": `'${image.prompt}'` } as React.CSSProperties}
										>
											<TerminalIcon className="size-4" />
										</p>
									</div>
								)}
							</div>
						</MarqueeItem>
					))}
				</SimpleMarquee>

				<SimpleMarquee
					className="w-full"
					baseVelocity={4}
					repeat={4}
					draggable={false}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowDownFactor={0.1}
					slowdownOnHover
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					scrollAwareDirection={true}
					scrollContainer={{ current: container! }}
					useScrollVelocity={true}
					direction="left"
				>
					{lastThird.map((image, i) => (
						<MarqueeItem key={i}>
							<div className="group relative cursor-pointer overflow-hidden" onClick={onCopy(image.prompt)}>
								<img
									src={image.url}
									alt={`Image ${i + firstThird.length + secondThird.length}`}
									className="aspect-[3/2] h-24 object-cover sm:h-[120px] md:h-[180px]"
								/>
								{image.prompt && (
									<div className="absolute inset-0 bg-black/50 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
										<p
											className={cn(
												"absolute top-2 right-2 left-2 hidden rounded-sm px-2 py-1 text-[14px] font-[350] text-zinc-100 transition-all duration-300 group-hover:block after:content-(--content)",
												"group-hover:line-clamp-2 sm:group-hover:line-clamp-3",
											)}
											// style={{ "--content": `'${image.prompt}'` } as React.CSSProperties}
										>
											<TerminalIcon className="size-4" />
											{image.prompt}
										</p>
									</div>
								)}
							</div>
						</MarqueeItem>
					))}
				</SimpleMarquee>
			</div>
		</div>
	);
}
