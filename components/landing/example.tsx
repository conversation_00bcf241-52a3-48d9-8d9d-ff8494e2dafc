"use client";

import { cn } from "@/lib/utils";
import { TerminalIcon } from "lucide-react";
import { ComponentProps } from "react";
import { toast } from "sonner";
import { useCopyToClipboard } from "usehooks-ts";

export function Example({
	title,
	description,
	images,
	columnClassName,
	className,
}: {
	title?: string;
	description?: string;
	images: {
		model?: string;
		url: string;
		prompt: string;
	}[];
	columnClassName?: string;
} & ComponentProps<"div">) {
	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error) => {
				toast.error("Failed to copy!", error);
			});
	};

	return (
		<div className={cn("container flex flex-col items-center gap-8 px-6 py-20", className)}>
			{title && (
				<div className="text-center">
					<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>
			)}

			<div className={cn("w-full columns-2 gap-3 space-y-3 md:columns-3 lg:columns-4", columnClassName)}>
				{images.map((image, index) => (
					<div key={index} className="group relative cursor-pointer break-inside-avoid overflow-hidden rounded-lg" onClick={onCopy(image.prompt)}>
						<img
							src={image.url}
							alt=""
							className="h-full w-full object-cover"
							loading="lazy"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
						/>
						<div className="bg-secondary-foreground/50 absolute inset-0 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
							<p
								className={cn(
									"absolute top-2 right-2 left-2 hidden rounded-sm px-2 py-1 text-[14px] font-[350] text-zinc-100 transition-all duration-300 group-hover:block after:content-(--content)",
									"group-hover:line-clamp-3 md:group-hover:line-clamp-4",
								)}
								style={{ "--content": `'${image.prompt}'` } as React.CSSProperties}
							>
								<TerminalIcon className="size-4" />
							</p>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
