"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

export default function TestimonialCarousel({
	testimonials,
}: {
	testimonials: {
		text: string;
		author: string;
	}[];
}) {
	const [value, setValue] = useState(testimonials[0].author);

	return (
		<div className="py-20">
			<div className="mx-auto max-w-4xl space-y-4">
				<div className="w-full">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" className="mx-auto h-6 w-6 text-teal-500">
						<path
							fill="currentColor"
							d="M3.516 7a3.5 3.5 0 1 1-3.5 3.5L0 10a7 7 0 0 1 7-7v2a4.97 4.97 0 0 0-3.536 1.464a5 5 0 0 0-.497.578q.269-.043.548-.043zm9 0a3.5 3.5 0 1 1-3.5 3.5L9 10a7 7 0 0 1 7-7v2a4.97 4.97 0 0 0-3.536 1.464a5 5 0 0 0-.497.578q.269-.043.549-.043z"
						/>
					</svg>
				</div>
				<Tabs className="space-y-4" value={value} onValueChange={setValue}>
					{testimonials.map((testimonial, index) => (
						<TabsContent value={testimonial.author} key={index}>
							<div className="container flex h-full w-full flex-col items-center justify-between gap-4">
								<p className="min-h-20 text-center text-3xl text-gray-900 duration-300">{testimonial.text}</p>
								<p className="mt-1 text-sm font-medium">{testimonial.author}</p>
							</div>
						</TabsContent>
					))}

					<TabsList className="w-full space-x-2 bg-transparent">
						{testimonials.map((testimonial, index) => (
							<TabsTrigger
								value={testimonial.author}
								key={index}
								className={cn(
									"h-2 w-10 px-0 py-0 shadow-none hover:bg-teal-100 data-[state=active]:bg-teal-500 data-[state=active]:shadow-none",
									value === testimonial.author ? "bg-teal-500" : "bg-gray-200",
								)}
							/>
						))}
					</TabsList>
				</Tabs>
			</div>
		</div>
	);

	// <TestimonialCarousel
	// 	testimonials={[
	// 		{
	// 			text: "First testimonial goes here. Praising your product or service and expressing satisfaction.",
	// 			author: "Ansub",
	// 		},
	// 		{
	// 			text: "Another testimonial goes here. Praising your product or service and expressing satisfaction.",
	// 			author: "Lex Collins",
	// 		},
	// 		{
	// 			text: "Third testimonial goes here. Praising your product or service and expressing satisfaction.",
	// 			author: "Alex Jones",
	// 		},
	// 		{
	// 			text: "Fourth testimonial goes here. Praising your product or service and expressing satisfaction.",
	// 			author: "John Doe",
	// 		},
	// 	]}
	// />;
}
