import { cn } from "@/lib/utils";

export const FeaturesBento = ({
	title,
	description,
	features,
}: {
	title: string;
	description?: string;
	features: {
		title: string;
		description: string;
		image: string;
		imageAlt: string;
	}[];
}) => {
	return (
		<div className="py-24">
			<div className="container flex flex-col items-center justify-center gap-12 px-6">
				<div className="max-w-4xl text-center">
					<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>
				<div className="mx-auto grid gap-4 sm:max-w-2xl lg:max-w-full lg:grid-cols-6">
					{features.map((feature, index) => (
						<div key={index} className={cn("bg-muted rounded-xl", index % 3 === 0 ? "lg:col-span-4" : "lg:col-span-2")}>
							<div className="flex h-full flex-col">
								<div className="flex h-[180px] overflow-hidden rounded-t-lg sm:h-[220px] md:h-[240px]">
									<img src={feature.image} alt={feature.imageAlt} className="h-full w-full object-cover" />
								</div>
								<div className="flex flex-col justify-start px-4 py-3 text-center md:px-8 md:py-6 lg:text-start">
									<h3 className="mb-2 text-lg font-medium">{feature.title}</h3>
									<p className="text-muted-foreground text-sm">{feature.description}</p>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
