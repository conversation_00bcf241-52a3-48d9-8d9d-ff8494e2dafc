type Feature = {
	title: string;
	description: string;
};

export default function FeaturesText({ features }: { features: Feature[] }) {
	return (
		<div className="container grid grid-cols-1 items-center gap-8 px-6 py-20 sm:grid-cols-2">
			{features.map((feature, index) => (
				<div key={index} className="bg-muted flex h-full w-full flex-col gap-2 rounded-xl p-6">
					<h2 className="text-3xl font-semibold">{feature.title}</h2>
					<p className="text-muted-foreground text-base">{feature.description}</p>
				</div>
			))}
		</div>
	);
}
