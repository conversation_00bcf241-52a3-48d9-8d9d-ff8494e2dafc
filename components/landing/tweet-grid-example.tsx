import { TweetGrid } from "@/components/ui/cult-ui/tweet-grid";

export default function TweetGridExample({ topic, tweets }: { topic: string; tweets: string[] }) {
	return (
		<div className="container flex flex-col items-center gap-12 px-6 py-20">
			<div className="text-center">
				<h2 className="text-[32px] font-semibold text-pretty">What People Say About {topic} on X</h2>
			</div>
			<TweetGrid tweets={tweets} />
		</div>
	);
}
