import React from "react";
import { B<PERSON><PERSON>rumb, Bread<PERSON>rumb<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { LucideIcon } from "lucide-react";

export default function BreadcrumbPath({ paths, current }: { paths: { title: string; href: string; icon?: LucideIcon }[]; current: string }) {
	return (
		<Breadcrumb>
			<BreadcrumbList className="gap-1 sm:gap-1">
				{paths.map((path, index) => (
					<React.Fragment key={index}>
						{index > 0 && <BreadcrumbSeparator />}
						<BreadcrumbItem>
							<NoPrefetchLink href={path.href!} className="text-foreground/60 hover:text-foreground flex flex-row items-center gap-1 text-[13px]">
								{/* {path.icon && <path.icon className="h-4 w-4" />} */}
								{path.title}
							</NoPrefetchLink>
						</BreadcrumbItem>
					</React.Fragment>
				))}
				<BreadcrumbSeparator />
				<BreadcrumbItem>
					<span className="text-foreground">{current}</span>
				</BreadcrumbItem>
			</BreadcrumbList>
		</Breadcrumb>
	);
}
