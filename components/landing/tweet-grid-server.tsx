import { TweetCard } from "@/components/ui/magicui/tweet-card";
import { cn } from "@/lib/utils";

export default function TweetGridServer({ topic, tweets, className }: { topic: string; tweets: string[]; className?: string }) {
	return (
		<div className="container flex flex-col items-center gap-12 px-6 py-20">
			<div className="text-center">
				<h2 className="text-[32px] font-semibold text-pretty">What People Say About {topic} on X</h2>
			</div>
			<div className={cn("columns-1 px-2 sm:columns-2 md:columns-3 xl:columns-4", className)}>
				{tweets.map((tweetId, i) => (
					<div key={`${tweetId}-${i}`} className="mb-2">
						<TweetCard id={tweetId} />
					</div>
				))}
			</div>
		</div>
	);
}
