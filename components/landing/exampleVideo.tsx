"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, TerminalIcon } from "lucide-react";
import { ComponentProps } from "react";
import { toast } from "sonner";
import { useCopyToClipboard } from "usehooks-ts";

export function ExampleVideo({
	title,
	description,
	videos,
	columnClassName,
	className,
}: {
	title?: string;
	description?: string;
	videos: {
		url: string;
		prompt: string;
	}[];
	columnClassName?: string;
} & ComponentProps<"div">) {
	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error) => {
				toast.error("Failed to copy!", error);
			});
	};

	return (
		<div className={cn("container flex flex-col items-center gap-8 px-6 py-20", className)}>
			{title && (
				<div className="text-center">
					<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>
			)}

			<div className={cn("w-full columns-1 gap-3 space-y-3 sm:columns-2 lg:columns-3", columnClassName)}>
				{videos.map((video, index) => (
					<div key={index} className="bg-muted overflow-hidden rounded-lg">
						<video
							src={video.url}
							muted
							className="h-full w-full rounded-t object-contain"
							onContextMenu={(e) => e.preventDefault()}
							onDragStart={(e) => e.preventDefault()}
							onMouseEnter={(e) => {
								(e.target as HTMLVideoElement).play();
								(e.target as HTMLVideoElement).controls = true;
							}}
							onMouseLeave={(e) => {
								(e.target as HTMLVideoElement).pause();
								(e.target as HTMLVideoElement).controls = false;
							}}
							controlsList="nodownload noplaybackrate"
							disablePictureInPicture
							disableRemotePlayback
							preload="metadata"
						/>
						{/* <div
							className={cn("rounded-sm px-4 py-2 text-[12px] font-[350] text-zinc-100 before:content-(--content)")}
							style={{ "--content": `'${video.prompt}'` } as React.CSSProperties}
						></div> */}
						<div className={cn("text-secondary-foreground rounded-sm px-4 py-2 text-[12px] font-[350]")}>
							<p className="flex flex-row items-center gap-1.5 pb-1 font-medium">
								<TerminalIcon className="h-3.5 w-3.5" />
							</p>
							{video.prompt}
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
