import type { PropsWithChildren } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/originui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface HintProps {
	title?: string;
	label: string;
	className?: string;
}

export const Hint = ({ title, label, children, className }: PropsWithChildren<HintProps>) => {
	return (
		<>
			<span className="hidden md:block">
				<TooltipProvider>
					<Tooltip delayDuration={100}>
						<TooltipTrigger asChild>{children}</TooltipTrigger>
						<TooltipContent className="dark max-w-[280px] py-3 shadow-none" side="top">
							<div className="space-y-1">
								{title && <p className="text-[13px] font-medium">{title}</p>}
								<p className="text-xs text-zinc-300">{label}</p>
							</div>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</span>
			<span className="block md:hidden">
				<Popover>
					<PopoverTrigger asChild>{children}</PopoverTrigger>
					<PopoverContent className="max-w-[280px] py-3 shadow-none" side="top">
						<div className="space-y-3">
							<div className="space-y-1">
								{title && <p className="text-[13px] font-medium">{title}</p>}
								<p className="text-xs text-zinc-300">{label}</p>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			</span>
		</>
	);
};
