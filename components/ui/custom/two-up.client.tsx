"use client";

import ReactCompareImage from "react-compare-image";

interface TwoUpClientProps {
	beforeSrc: string;
	afterSrc: string;
}

export default function TwoUpClient({ beforeSrc, afterSrc }: TwoUpClientProps) {
	return (
		<ReactCompareImage
			aspectRatio="taller"
			leftImage={beforeSrc}
			rightImage={afterSrc}
			// leftImageLabel="Before"
			// rightImageLabel="After"
			handleSize={36}
			leftImageCss={{
				objectFit: "contain",
				objectPosition: "center",
			}}
			rightImageCss={{
				objectFit: "contain",
				objectPosition: "center",
			}}
		/>
	);
}
