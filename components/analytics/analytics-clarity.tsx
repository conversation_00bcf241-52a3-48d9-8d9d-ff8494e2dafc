"use client";

import { useEffect } from "react";
import { clarity } from "react-microsoft-clarity";
import { CLARITY_TOKEN } from "@/lib/constants";
import { usePathname } from "next/navigation";

export function AnalyticsClarity() {
	const pathname = usePathname();
	useEffect(() => {
		if (process.env.NODE_ENV !== "production") return;
		if (pathname.startsWith("/admin")) return;
		clarity.init(CLARITY_TOKEN);
	}, []);
	return <></>;
}
