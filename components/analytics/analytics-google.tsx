"use client";

import { GoogleTagManager } from "@next/third-parties/google";
import { GTM_TOKEN, WEBDOMAIN } from "@/lib/constants";
import { usePathname } from "next/navigation";

export function AnalyticsGoogle() {
	const pathname = usePathname();
	if (pathname.startsWith("/admin")) {
		console.log("skip admin page");
		return null;
	}
	return (
		<GoogleTagManager gtmId={`GTM-${GTM_TOKEN}`} gtmScriptUrl={`https://${WEBDOMAIN}/zdata/`} />
		// <div className="container">
		// 	<Script src={`https://www.googletagmanager.com/gtag/js?id=G-${GA_TOKEN}`} />
		// 	<Script id="google-analytics">
		// 		{`
		// 			window.dataLayer = window.dataLayer || [];
		// 			function gtag(){dataLayer.push(arguments);}
		// 			gtag('js', new Date());

		// 			gtag('config', 'G-${GA_TOKEN}');
		// 		`}
		// 	</Script>
		// </div>
	);
}
