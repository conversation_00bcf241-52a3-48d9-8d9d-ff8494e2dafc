"use client";

import React, { useState } from "react";
import { ChevronDown, CreditCard, Crown, Power } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useSession } from "@/lib/auth-client";
import { Separator } from "@/components/ui/separator";
import { useSignOut } from "@/hooks/use-signout";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { usePathname } from "next/navigation";
import { Logo } from "../logo";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

interface MenuItem {
	name: string;
	href?: string;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: {
		name: string;
		href: string;
		description?: string;
	}[];
}

const menuItems: MenuItem[] = [
	{ name: "Create", href: "/app/create" },
	{ name: "Sketch", href: "/app/sketch" },
	// { name: "My creations", href: "/my-creations" },
];

export default function DashboardHeader() {
	const pathname = usePathname();
	const { data: session } = useSession();
	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);
	const { handleSignOut } = useSignOut();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { user } = useUserStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	return (
		<header className="sticky top-0 z-20 w-full border-b border-transparent bg-neutral-50 transition-colors duration-300">
			<div className="flex h-16 flex-wrap items-center justify-between px-4 md:px-6">
				<div className="flex flex-row items-center gap-8">
					<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo />
						<span className="text-lg font-medium">{WEBNAME}</span>
					</NoPrefetchLink>
				</div>

				<nav className="hidden md:flex">
					<ul className="flex space-x-8">
						{menuItems.map((item) => (
							<li key={item.name}>
								<NoPrefetchLink
									href={item.href || "#"}
									className={cn(
										"text-sm font-medium transition-colors hover:text-primary",
										item.href === pathname ? "rounded-full bg-neutral-800 px-6 py-2.5 text-white hover:text-white" : "",
									)}
								>
									{item.name}
								</NoPrefetchLink>
							</li>
						))}
					</ul>
				</nav>

				<div className="flex items-center gap-3 md:order-2">
					{session?.user ? (
						<>
							{/* <div className="relative shrink-0">
								<Circle className="size-8 text-indigo-500" strokeWidth={1.5} />
								<div className="bg-neutral-875 absolute inset-0 m-[2px] flex items-center justify-center rounded-full">
									<span className="text-[10px] tracking-tighter text-neutral-100">20</span>
								</div>
							</div> */}
							{user?.membershipId === MembershipID.Free && (
								<button className="relative flex items-center gap-2 text-xs" onClick={() => setPlanBoxOpen(true)}>
									<Crown className="size-3.5 fill-current text-yellow-500" />
									Upgrade
								</button>
							)}
							<DropdownMenu modal={false}>
								<DropdownMenuTrigger asChild className="cursor-pointer">
									<div className="flex shrink-0 flex-row items-center gap-2">
										<Avatar className="h-7 w-7">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
									</div>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[280px] p-0" align="end" forceMount>
									<div className="flex gap-5 p-5">
										<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<div className="flex min-w-0 flex-1 flex-col items-start">
											<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
											<p className="mt-1 truncate text-xs text-muted-foreground">{session?.user.email ?? "--"}</p>
										</div>
									</div>
									{user?.membershipId === MembershipID.Free && (
										<div className="px-[24px] pb-5">
											<Button size="sm" className="w-full bg-indigo-500 hover:bg-indigo-600" onClick={() => setPlanBoxOpen(true)}>
												Get a plan
											</Button>
										</div>
									)}
									<Separator />

									<NoPrefetchLink
										href="/app/user/my-subscriptions"
										className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-muted"
									>
										<div className="flex flex-row items-center">
											<CreditCard className="mr-5 h-4 w-4 shrink-0" />
											Subscription
										</div>
										<p className="flex items-center gap-1 rounded bg-muted px-2 py-1 text-xs font-medium">{user?.membershipFormatted}</p>
									</NoPrefetchLink>
									{/* <NoPrefetchLink
										href={FEEDBACK_URL}
										target="_blank"
										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
									>
										<MessageCircle className="mr-5 h-4 w-4 shrink-0" />
										Got Feedback
									</NoPrefetchLink> */}

									<Separator />

									<button
										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
										onClick={handleSignOut}
									>
										<Power className="mr-5 h-4 w-4 shrink-0" />
										Sign out
									</button>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					) : (
						<Button onClick={() => setSignInBoxOpen(true)}>Sign In</Button>
					)}
				</div>
			</div>
		</header>
	);
}
