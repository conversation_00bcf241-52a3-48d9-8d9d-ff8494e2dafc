"use client";

import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useEffect } from "react";

export function InitializeUser() {
	const { data: session, isPending } = useSession();
	const { user, refreshUser, isLoaded } = useUserStore();

	useEffect(() => {
		console.log("isPending:", isPending);
		if (isPending) return;
		if (session && !user) {
			refreshUser();
		}
	}, [isPending]);

	return <></>;
}
