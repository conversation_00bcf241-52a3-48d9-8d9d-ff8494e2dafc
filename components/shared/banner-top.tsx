"use client";

import { useEffect, useState } from "react";
import { useCookies } from "next-client-cookies";
import { Banner, BannerAction, BannerClose, BannerTitle } from "@/components/ui/kibo-ui/banner";
import { OSS_URL_HOST } from "@/lib/constants";
import { ArrowRight } from "lucide-react";
import { useRouter } from "nextjs-toploader/app";
import { WAN_2_2_TURBO } from "@/lib/utils-video-model";

export function BannerTop() {
	const router = useRouter();
	const cookies = useCookies();
	const BANNER_COOKIE_KEY = "banner-show";
	const nowBannerId = "wan-v2.2";
	const [showBanner, setShowBanner] = useState<boolean>(false);

	useEffect(() => {
		const bannerShow = cookies.get(BANNER_COOKIE_KEY);
		console.log("bannerShow: ", bannerShow);

		if (!bannerShow) {
			setShowBanner(true);
		}
		if (bannerShow !== nowBannerId) {
			setShowBanner(true);
		}
	}, []);

	const handleCloseBanner = () => {
		setShowBanner(false);
		if (process.env.NODE_ENV !== "development") {
			cookies.set(BANNER_COOKIE_KEY, nowBannerId, {
				path: "/",
			});
		}
	};
	if (showBanner) {
		return (
			<Banner className="bg-indigo-800/20 py-1">
				{/* <BannerIcon icon={CircleAlert} /> */}

				<div className="flex w-full flex-row items-center justify-center gap-1 sm:gap-2">
					<img className="size-4 text-white" src={`${OSS_URL_HOST}icon/model/qwen-color.webp`} alt="" />
					<BannerTitle className="font-[350] text-zinc-100 sm:flex-none">
						<span className="font-semibold text-white">Wan 2.2</span> is now available on Dreampik.art!
					</BannerTitle>
					<BannerAction
						className="hover:bg-primary cursor-pointer rounded-full"
						variant="default"
						onClick={() => router.push(`/image-to-video?model=${WAN_2_2_TURBO.id}`)}
					>
						Try Now <ArrowRight />
					</BannerAction>
				</div>
				<BannerClose onClick={handleCloseBanner} variant="secondary" className="hover:text-secondary-foreground cursor-pointer" />
			</Banner>
		);
	}
	return <></>;
}
