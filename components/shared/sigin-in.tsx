"use client";

import { useState } from "react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Icons } from "../icon/icons";
import { WEBNAME } from "@/lib/constants";
import { signIn } from "@/lib/auth-client";
import { toast } from "sonner";
import { Logo } from "@/components/logo";
import { usePathname } from "next/navigation";
import { sendGTMEvent } from "@next/third-parties/google";
import { EVENT_SIGN_IN } from "@/lib/track-events";

export default function SignIn() {
	const [isSigningIn, setIsSigningIn] = useState(false);
	const pathname = usePathname();

	return (
		<div className="mx-auto flex w-full flex-col justify-center gap-2 space-y-6 text-center">
			<div className="flex flex-row items-center justify-center gap-2 text-2xl font-semibold">
				<Logo className="size-10" />
				<span className="">{WEBNAME}</span>
			</div>
			{/* <div className="mx-auto flex w-full flex-col items-center gap-10">
			<div className="flex w-full flex-col justify-center gap-4 px-6 pb-8 pt-10 sm:px-8">
				<h1 className="text-6xl font-semibold text-black">{WEBNAME}</h1>
 			</div> */}

			<div className="text-md text-muted-foreground">
				{/* Start for free. No credit card needed. */}
				Sign in now to unlock creativity.
			</div>

			<form
				action={async () => {
					console.log("signIn");

					sendGTMEvent({
						event: EVENT_SIGN_IN,
					});

					const { data, error } = await signIn.social({
						provider: "google",
						// callbackURL: `/api/auth/redirect?next=${pathname}`,
						// callbackURL: `/api/auth/redirect`,
						// callbackURL: ROUTE_PATH_SIGN_IN_AFTER,
						callbackURL: pathname,
					});
					if (error) {
						console.log("error:", error.statusText);
						setIsSigningIn(false);
						toast.error(error.statusText);
						return;
					}
				}}
				onSubmit={() => setIsSigningIn(true)}
				className="flex w-full justify-center gap-6 px-6 sm:px-8"
			>
				<Button
					type="submit"
					// className="w-full rounded-full"
					className="bg-foreground hover:bg-foreground/80 h-auto w-full max-w-[320px] cursor-pointer gap-3 rounded-lg border py-2 text-base"
					size="lg"
					{...{ disabled: isSigningIn }}
				>
					{isSigningIn ? <Loader2 className="h-6 w-6 shrink-0 animate-spin" /> : <Icons.google className="h-6 w-6 shrink-0" />}
					<span className="">Sign in with Google</span>
				</Button>
			</form>
		</div>
	);
}
