import { RectangleVertical, Square, RectangleHorizontal } from "lucide-react";
import { OSS_URL_HOST } from "./constants";

export const getAspectRatioIcon = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return RectangleHorizontal;
		case "9:16":
			return RectangleVertical;
		case "1:1":
			return Square;
		case "9:21":
			return RectangleVertical;
		default:
			throw new Error("Invalid aspect ratio");
	}
};
export const getAspectRatioClass = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return "aspect-video";
		case "9:16":
			return "aspect-9/16";
		case "1:1":
			return "aspect-square";
		case "9:21":
			return "aspect-9/21";
		default:
			throw new Error("Invalid aspect ratio");
	}
};

// export type VideoModel = {
// 	name: string;
// 	id: string; // for api, db
// 	model: string; // for image generatiton third platform api
// 	modelStyle?: {
// 		id: string | null;
// 		name: string;
// 	}[];
// 	aspectRatioAll?: string[];
// 	durationAll: number[];
// 	resolutionAll?: string[];
// 	credits: {
// 		perSecond?: number;
// 		onetime?: number;
// 		per_second_with_resolution?: {
// 			resolution: string;
// 			per_second: number;
// 		}[];
// 		onetime_with_resolution?: {
// 			resolution: string;
// 			onetime: number;
// 		}[];
// 	};
// 	creditsMethod?: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
// 	textToVideo: boolean;
// 	imageToVideo: boolean;
// 	imageToVideoNotAspectRatio?: boolean;
// 	time?: number; // seconds
// 	logo?: string;
// 	new?: boolean;
// };

type CreditsType = {
	perSecond?: number;
	onetime?: number;
	per_second_with_resolution?: {
		resolution: string;
		per_second: number;
	}[];
	onetime_with_resolution?: {
		resolution: string;
		onetime: number;
	}[];
};
export const getVideoModelCredits = (creditsType: CreditsType, creditsMethod: string, duration?: number, resolution?: string | null) => {
	if (creditsMethod === "per_second") {
		return duration! * creditsType.perSecond!;
	}
	if (creditsMethod === "onetime") {
		return creditsType.onetime!;
	}
	if (creditsMethod === "per_second_with_resolution") {
		const resolutionCredits = creditsType.per_second_with_resolution?.find((r) => r.resolution === resolution);
		if (!resolutionCredits) {
			throw new Error("Invalid resolution");
		}
		return duration! * resolutionCredits.per_second;
	}
	if (creditsMethod === "onetime_with_resolution") {
		const resolutionCredits = creditsType.onetime_with_resolution?.find((r) => r.resolution === resolution);
		if (!resolutionCredits) {
			throw new Error("Invalid resolution");
		}
		return resolutionCredits.onetime;
	}
	throw new Error("Invalid credits method");
};
export type VideoModel = {
	name: string;
	id: string; // for api, db
	model: string; // for image generatiton third platform api
	modelStyle?: {
		id: string | null;
		name: string;
	}[];
	textToVideo?: {
		aspectRatioAll?: string[];
		durationAll: number[];
		resolutionAll?: string[];
		credits: CreditsType;
		creditsMethod: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
	};
	imageToVideo?: {
		aspectRatioAll?: string[];
		durationAll: number[];
		resolutionAll?: string[];
		credits: CreditsType;
		creditsMethod: "per_second" | "onetime" | "per_second_with_resolution" | "onetime_with_resolution";
		endFrame?: boolean;
	};
	time?: number; // seconds
	logo?: string;
	new?: boolean;
};
export const LTX_13B_DISTILLED: VideoModel = {
	name: "LTX Video",
	id: "ltxv-13b-098-distilled",
	model: "ltxv-13b-098-distilled",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "720p"],
		credits: { perSecond: 3 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "720p"],
		credits: { perSecond: 3 },
		creditsMethod: "per_second",
	},
	time: 30,
	logo: `${OSS_URL_HOST}icon/model/lightricks-dark.webp`,
};
export const KLING_2_1_MASTER: VideoModel = {
	name: "kling 2.1 Master",
	id: "kling-2.1-master",
	model: "kling-2.1-master",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 28 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 28 },
		creditsMethod: "per_second",
	},
	time: 60 * 5,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_1_PRO: VideoModel = {
	name: "kling 2.1 Pro",
	id: "kling-2.1-pro",
	model: "kling-2.1-pro",
	textToVideo: undefined,
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 9 },
		creditsMethod: "per_second",
	},
	time: 60 * 3,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_1_STANDARD: VideoModel = {
	name: "kling 2.1 Standard",
	id: "kling-2.1-standard",
	model: "kling-2.1-standard",
	textToVideo: undefined,
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 5 },
		creditsMethod: "per_second",
	},
	time: 90,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_MASTER: VideoModel = {
	name: "kling 2 Master",
	id: "kling-2-master",
	model: "kling-2-master",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 28 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5, 10],
		resolutionAll: undefined,
		credits: { perSecond: 28 },
		creditsMethod: "per_second",
	},
	time: 60 * 5,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const GOOGLE_VEO_3: VideoModel = {
	name: "Veo 3",
	id: "google-veo-3",
	model: "veo3",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [8],
		resolutionAll: ["720p", "1080p"],
		credits: { perSecond: 75 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [8],
		resolutionAll: ["720p"],
		credits: { perSecond: 75 },
		creditsMethod: "per_second",
	},
	time: 60 * 2,
	logo: `${OSS_URL_HOST}icon/model/deepmind-color.webp`,
};
export const GOOGLE_VEO_3_FAST: VideoModel = {
	name: "Veo 3 Fast",
	id: "google-veo-3-fast",
	model: "veo3/fast",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [8],
		resolutionAll: ["720p", "1080p"],
		credits: { perSecond: 40 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [8],
		resolutionAll: ["720p"],
		credits: { perSecond: 40 },
		creditsMethod: "per_second",
	},
	time: 90,
	logo: `${OSS_URL_HOST}icon/model/deepmind-color.webp`,
};
export const SEEDANCE_1_LITE: VideoModel = {
	name: "Seedance 1 Lite",
	id: "seedance-1-lite",
	model: "seedance/v1/lite",
	textToVideo: {
		aspectRatioAll: ["16:9", "1:1", "9:16", "9:21"],
		durationAll: [3, 5, 8, 10],
		resolutionAll: ["720p"],
		credits: { perSecond: 4 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [3, 5, 8, 10],
		resolutionAll: ["720p"],
		credits: { perSecond: 4 },
		creditsMethod: "per_second",
		endFrame: true,
	},
	time: 60,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
};
export const SEEDANCE_1_PRO: VideoModel = {
	name: "Seedance 1 Pro",
	id: "seedance-1-pro",
	model: "seedance/v1/pro",
	textToVideo: {
		aspectRatioAll: ["16:9", "1:1", "9:16", "9:21"],
		durationAll: [3, 5, 8, 10],
		resolutionAll: ["720p", "1080p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "720p",
					per_second: 9,
				},
				{
					resolution: "1080p",
					per_second: 13,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [3, 5, 8, 10],
		resolutionAll: ["720p", "1080p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "720p",
					per_second: 9,
				},
				{
					resolution: "1080p",
					per_second: 13,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	time: 60 * 2,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
};
export const PIXVERSE_4_5_FAST: VideoModel = {
	name: "Pixverse 4.5 Fast",
	id: "pixverse-4.5-fast",
	model: "pixverse/v4.5/fast",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["360p", "480p", "720p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "360p",
					per_second: 6,
				},
				{
					resolution: "480p",
					per_second: 6,
				},
				{
					resolution: "720p",
					per_second: 8,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	imageToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["360p", "480p", "720p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "360p",
					per_second: 6,
				},
				{
					resolution: "480p",
					per_second: 6,
				},
				{
					resolution: "720p",
					per_second: 8,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	time: 100,
	logo: `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
};
export const PIXVERSE_4_5: VideoModel = {
	name: "Pixverse 4.5",
	id: "pixverse-4.5",
	model: "pixverse/v4.5",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5, 8],
		resolutionAll: ["360p", "480p", "720p", "1080p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "360p",
					per_second: 3,
				},
				{
					resolution: "480p",
					per_second: 3,
				},
				{
					resolution: "720p",
					per_second: 4,
				},
				{
					resolution: "1080p",
					per_second: 8,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	imageToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5, 8],
		resolutionAll: ["360p", "480p", "720p", "1080p"],
		credits: {
			per_second_with_resolution: [
				{
					resolution: "360p",
					per_second: 3,
				},
				{
					resolution: "480p",
					per_second: 3,
				},
				{
					resolution: "720p",
					per_second: 4,
				},
				{
					resolution: "1080p",
					per_second: 8,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	time: 100,
	logo: `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
};
export const HAILUO_2_STANDARD: VideoModel = {
	name: "Hailuo 2 Standard",
	id: "hailuo-02-standard",
	model: "hailuo-02/standard",
	textToVideo: {
		aspectRatioAll: undefined,
		durationAll: [6, 10],
		resolutionAll: ["768p"],
		credits: { perSecond: 5 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [6, 10],
		resolutionAll: ["512p", "768p"],
		// credits: { perSecond: 5 },
		// creditsMethod: "per_second",
		credits: {
			per_second_with_resolution: [
				{
					resolution: "512p",
					per_second: 2,
				},
				{
					resolution: "768p",
					per_second: 5,
				},
			],
		},
		creditsMethod: "per_second_with_resolution",
	},
	time: 60 * 4,
	logo: `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
};
export const HAILUO_2_PRO: VideoModel = {
	name: "Hailuo 2 Pro",
	id: "hailuo-02-pro",
	model: "hailuo-02/pro",
	textToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5],
		resolutionAll: ["1080p"],
		credits: { perSecond: 8 },
		creditsMethod: "per_second",
	},
	imageToVideo: {
		aspectRatioAll: undefined,
		durationAll: [5],
		resolutionAll: ["1080p"],
		credits: { perSecond: 8 },
		creditsMethod: "per_second",
	},
	time: 60 * 6,
	logo: `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
};
// export const WAN_2_2_LITE: VideoModel = {
// 	name: "Wan 2.2 Lite",
// 	id: "wan-v2.2-5b",
// 	model: "wan/v2.2-5b",
// 	credits: { onetime: 15 },
// 	creditsMethod: "onetime",
// 	aspectRatioAll: ["16:9", "9:16", "1:1"],
// 	durationAll: [5],
// 	resolutionAll: ["580p", "720p"],
// 	textToVideo: true,
// 	imageToVideo: true,
// 	time: 30,
// 	logo: `${OSS_URL_HOST}icon/model/qwen-color.webp`,
// 	new: true,
// };
export const WAN_2_2_TURBO: VideoModel = {
	name: "Wan 2.2 Turbo",
	id: "wan-v2.2-a14b-turbo",
	model: "wan/v2.2-a14b-turbo",
	textToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "580p", "720p"],
		credits: {
			onetime_with_resolution: [
				{
					resolution: "480p",
					onetime: 6,
				},
				{
					resolution: "580p",
					onetime: 8,
				},
				{
					resolution: "720p",
					onetime: 11,
				},
			],
		},
		creditsMethod: "onetime_with_resolution",
	},
	imageToVideo: {
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "580p", "720p"],
		credits: {
			onetime_with_resolution: [
				{
					resolution: "480p",
					onetime: 6,
				},
				{
					resolution: "580p",
					onetime: 8,
				},
				{
					resolution: "720p",
					onetime: 11,
				},
			],
		},
		creditsMethod: "onetime_with_resolution",
	},
	time: 40,
	logo: `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	new: true,
};
export const WAN_2_2: VideoModel = {
	name: "Wan 2.2",
	id: "wan-v2.2-a14b",
	model: "wan/v2.2-a14b",
	textToVideo: {
		credits: { perSecond: 8 },
		creditsMethod: "per_second",
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "580p", "720p"],
	},
	imageToVideo: {
		credits: { perSecond: 8 },
		creditsMethod: "per_second",
		aspectRatioAll: ["16:9", "9:16", "1:1"],
		durationAll: [5],
		resolutionAll: ["480p", "580p", "720p"],
	},
	time: 60 * 3,
	logo: `${OSS_URL_HOST}icon/model/qwen-color.webp`,
	new: true,
};

const videoModels: VideoModel[] = [
	WAN_2_2_TURBO,
	WAN_2_2,
	LTX_13B_DISTILLED,
	GOOGLE_VEO_3,
	GOOGLE_VEO_3_FAST,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	SEEDANCE_1_PRO,
	SEEDANCE_1_LITE,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	KLING_2_MASTER,
];
export const textToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.textToVideo);
export const imageToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.imageToVideo);

export const getVideoModel = (id: string): VideoModel => {
	const videoModel = videoModels.find((videoModel) => videoModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("videoModel: ", videoModel);
	}
	if (!videoModel) {
		throw new Error("Video model is not found.");
	}
	return videoModel;
};

export const pageVideoModels: VideoModel[] = [
	WAN_2_2_TURBO,
	WAN_2_2,
	LTX_13B_DISTILLED,
	GOOGLE_VEO_3_FAST,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	SEEDANCE_1_PRO,
	SEEDANCE_1_LITE,
];

export const getVideoModelInPage = (id: string): VideoModel => {
	const videoModel = pageVideoModels.find((videoModel) => videoModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("videoModel: ", videoModel);
	}
	if (!videoModel) {
		throw new Error("Video model is not found.");
	}
	return videoModel;
};
