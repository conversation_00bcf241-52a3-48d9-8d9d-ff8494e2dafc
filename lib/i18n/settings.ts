// export const fallbackLng = 'en'
// export const languages = [fallbackLng, 'es', 'it']
export const defaultNS = 'common'
export const cookieName = 'NEXT_LOCALE'
import { i18nConfig } from "@/i18n-config"

export function getOptions (lng = i18nConfig.defaultLocale, ns: string | string[] = defaultNS) {

	return {
		// debug: true,
		supportedLngs: i18nConfig.locales,
		// preload: languages,
		fallbackLng: i18nConfig.defaultLocale,
		lng: lng,
		fallbackNS: defaultNS,
		defaultNS,
		ns,
		// backend: {
		//   projectId: '01b2e5e8-6243-47d1-b36f-963dbb8bcae3'
		// }
	}
}
