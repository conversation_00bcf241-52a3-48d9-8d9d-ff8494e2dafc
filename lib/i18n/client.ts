'use client'

import { useEffect, useState } from 'react'
import i18next, { FlatNamespace, KeyPrefix } from 'i18next'
import { initReactI18next, useTranslation as useTranslationOrg, UseTranslationOptions, UseTranslationResponse, FallbackNs } from 'react-i18next'
// import { useCookies } from 'react-cookie'
import resourcesToBackend from 'i18next-resources-to-backend'
// import LanguageDetector from 'i18next-browser-languagedetector'
// import { getOptions, cookieName } from './settings'
import { i18nConfig } from "@/i18n-config"

const runsOnServerSide = typeof window === 'undefined'

i18next
.use(initReactI18next)
// .use(LanguageDetector)
.use(resourcesToBackend((language: string, namespace: string) => import(`/public/locales/${language}/${namespace}.json`)))
// .use(LocizeBackend) // locize backend could be used on client side, but prefer to keep it in sync with server side
// .init({
// 	...getOptions(),
// 	lng: undefined, // let detect the language on client side
// 	// detection: {
// 	// 	order: ['path', 'htmlTag', 'cookie', 'navigator'],
// 	// },
// 	preload: runsOnServerSide ? i18nConfig.locales : []
// })
.init({
	lng: undefined, // let detect the language on client side
	// debug: true,
	fallbackLng: i18nConfig.defaultLocale,
	supportedLngs: i18nConfig.locales,
	fallbackNS: "common",
	defaultNS: "common",
	// ns,
	preload: i18nConfig.locales,
})

export function useTranslation<
	Ns extends FlatNamespace,
	KPrefix extends KeyPrefix<FallbackNs<Ns>> = undefined
>(
	lng: string,
	ns?: Ns,
	options?: UseTranslationOptions<KPrefix>,
): UseTranslationResponse<FallbackNs<Ns>, KPrefix> {
	const { i18n } = useTranslationOrg(ns, options);

  // 在客户端，监听和同步语言变化
  	useEffect(() => {
		if (!runsOnServerSide && lng && i18n.resolvedLanguage !== lng) {
			i18n.changeLanguage(lng);
		}
	}, [lng, i18n]);

	return useTranslationOrg(ns, options);

	// const [cookies, setCookie] = useCookies([cookieName])
	// const ret = useTranslationOrg(ns, options)
	// const { i18n } = ret
	// if (runsOnServerSide && lng && i18n.resolvedLanguage !== lng) {
	// 	i18n.changeLanguage(lng)
	// } else {
	// 	// eslint-disable-next-line react-hooks/rules-of-hooks
	// 	const [activeLng, setActiveLng] = useState(i18n.resolvedLanguage)
		
	// 	// eslint-disable-next-line react-hooks/rules-of-hooks
	// 	useEffect(() => {
	// 		if (activeLng === i18n.resolvedLanguage) return
	// 		setActiveLng(i18n.resolvedLanguage)
	// 	}, [activeLng, i18n.resolvedLanguage])

	// 	// eslint-disable-next-line react-hooks/rules-of-hooks
	// 	useEffect(() => {
	// 		if (!lng || i18n.resolvedLanguage === lng) return
	// 		i18n.changeLanguage(lng)
	// 	}, [lng, i18n])

	// 	// eslint-disable-next-line react-hooks/rules-of-hooks
	// 	useEffect(() => {
	// 		if (cookies.i18next === lng) return
	// 		setCookie(cookieName, lng, { path: '/' })
	// 	}, [lng, cookies.i18next])
	// }
	// return ret
}
