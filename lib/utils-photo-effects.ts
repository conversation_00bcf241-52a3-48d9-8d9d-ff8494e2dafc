import { OSS_URL_HOST } from "./constants";

// photo style model
export enum PhotoStyleID {
	Ghibli = "ghibli",
	Anime = "anime",
	Pixel = "pixel",
	BabyFilter = "baby-filter",
	Lego = "lego",
	Simpsons = "simpsons",
	Painting = "painting",
	Sketch = "sketch",
	Line = "line",
	Disney = "disney",
}
export type PhotoEffectType = {
	id: string;
	apiEndpoint: string;
	name: string;
	pageName: string;
	pageUrl: string;
	demoImage?: string;
	sampleImage?: string;
	notPage?: boolean;
};
export const allPhotoEffects: PhotoEffectType[] = [
	{
		id: PhotoStyleID.Anime,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Anime",
		pageName: "AI Anime Filter",
		pageUrl: "/photo-effects/anime-filter",
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/anime-filter.webp`,
		sampleImage: `${OSS_URL_HOST}mkt/pages/photo-effects/anime-filter/anime-filter-sample.webp`,
	},
	{
		id: PhotoStyleID.BabyFilter,
		apiEndpoint: "/api/v1/image/photo-effect/baby-filter",
		name: "Baby Filter",
		pageName: "AI Baby Filter",
		pageUrl: "/photo-effects/baby-filter",
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/baby-filter.avif`,
		sampleImage: `${OSS_URL_HOST}mkt/pages/photo-effects/baby-filter/baby-filter-sample.avif`,
	},
	{
		id: PhotoStyleID.Pixel,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Pixel",
		pageName: "Pixel Art Generator",
		pageUrl: "/photo-effects/photo-to-pixel-art",
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/pixel-art.webp`,
	},
	{
		id: PhotoStyleID.Lego,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Lego",
		pageName: "AI Lego Filter",
		pageUrl: `/photo-effects/lego-filter`,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/lego-filter.webp`,
		sampleImage: `${OSS_URL_HOST}mkt/pages/photo-effects/lego-filter/lego-filter-sample.webp`,
	},
	{
		id: PhotoStyleID.Ghibli,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Ghibli",
		pageName: "Ghibli AI Generator",
		pageUrl: "/photo-effects/studio-ghibli",
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/studio-ghibli.webp`,
	},
	{
		id: PhotoStyleID.Simpsons,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Simpsons",
		pageName: "Simpsons Character Creator",
		pageUrl: `/photo-effects/restyler?styleId=${PhotoStyleID.Simpsons}`,
		notPage: true,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/simpsons-character.webp`,
		sampleImage: `${OSS_URL_HOST}mkt/pages/photo-effects/simpsons-character/simpsons-character-sample.webp`,
	},
	{
		id: PhotoStyleID.Painting,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Painting",
		pageName: "AI Photo to Painting",
		pageUrl: `/photo-effects/restyler?styleId=${PhotoStyleID.Painting}`,
		notPage: true,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/painting.webp`,
	},
	{
		id: PhotoStyleID.Sketch,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Sketch",
		pageName: "AI Photo to Sketch",
		pageUrl: `/photo-effects/restyler?styleId=${PhotoStyleID.Sketch}`,
		notPage: true,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/sketch.webp`,
	},
	{
		id: PhotoStyleID.Line,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Line",
		pageName: "AI Photo to Line Drawing",
		pageUrl: `/photo-effects/restyler?styleId=${PhotoStyleID.Line}`,
		notPage: true,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/line-drawing.webp`,
	},
	{
		id: PhotoStyleID.Disney,
		apiEndpoint: "/api/v1/image/photo-effect",
		name: "Disney",
		pageName: "AI Disney Filter",
		pageUrl: `/photo-effects/restyler?styleId=${PhotoStyleID.Disney}`,
		notPage: true,
		demoImage: `${OSS_URL_HOST}mkt/pages/photo-effects/app/disney-filter.webp`,
	},
];

export interface PhotoEffectLora {
	id: string;
	prompt: string;
	loraUrl?: string;
}

// https://huggingface.co/Owen777/Kontext-Style-Loras/tree/main
export const photoEffectLoras: PhotoEffectLora[] = [
	{
		id: PhotoStyleID.Anime,
		prompt: "Convert to anime art style with large eyes and stylized features",
	},
	{
		id: PhotoStyleID.Pixel,
		prompt: "Turn this image into the Pixel style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Pixel_lora_weights.safetensors",
	},
	{
		id: PhotoStyleID.Lego,
		prompt: "convert to lego style",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/LEGO_lora_weights.safetensors",
	},
	{
		id: PhotoStyleID.Ghibli,
		prompt: "Turn this image into the Ghibli style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Ghibli_lora_weights.safetensors",
	},
	{
		id: PhotoStyleID.Simpsons,
		prompt: "convert to Simpsons cartoon style",
	},
	{
		id: PhotoStyleID.Painting,
		// prompt: "Turn this image into the Oil Painting style.",
		// loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Oil_Painting_lora_weights.safetensors",
		prompt: "Convert this image into heavy oil paint brush strokes style",
		loraUrl: "https://huggingface.co/gokaygokay/Oil-Paint-Kontext-Dev-LoRA/resolve/main/oil_painting.safetensors",
	},
	{
		id: PhotoStyleID.Sketch,
		prompt: "Turn this image into a pencil sketch style while maintaining the original composition and object placement.",
	},
	{
		id: PhotoStyleID.Line,
		prompt: "Turn this image into the Line style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Line_lora_weights.safetensors",
	},
	{
		id: PhotoStyleID.Disney,
		prompt: "Turn this image into the Disney style while maintaining the original composition and object placement.",
	},
	{
		id: "snoopy",
		prompt: "Turn this image into the Snoopy style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Snoopy_lora_weights.safetensors",
	},
	{
		id: "jojo",
		prompt: "Turn this image into the JoJo style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Jojo_lora_weights.safetensors",
	},
	{
		id: "clay",
		prompt: "Turn this image into the Clay style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/Clay_Toy_lora_weights.safetensors",
	},
	{
		id: "americancartoon",
		prompt: "Turn this image into the American Cartoon style.",
		loraUrl: "https://huggingface.co/Owen777/Kontext-Style-Loras/resolve/main/American_Cartoon_lora_weights.safetensors",
	},
	{
		id: "broccoli",
		prompt: "Change hair to a broccoli haircut",
		loraUrl: "https://huggingface.co/fal/Broccoli-Hair-Kontext-Dev-LoRA/resolve/main/broccoli-hair-kontext-dev-lora.safetensors",
	},
	{
		id: "plushie",
		prompt: "Convert to plushie style",
		loraUrl: "https://huggingface.co/fal/Plushie-Kontext-Dev-LoRA/resolve/main/plushie-kontext-dev-lora.safetensors",
	},
	{
		id: "wojak",
		prompt: "Convert to wojak style drawing",
		loraUrl: "https://huggingface.co/fal/Wojak-Kontext-Dev-LoRA/resolve/main/wojak-kontext-dev-lora.safetensors",
	},
	{
		id: "fluffy",
		prompt: "make this object fluffy",
	},
	{
		id: "glassprism",
		prompt: "make the character/object look like it was made out of glass, black background",
	},
];

export const getPhotoEffectLoraAndPrompt = (id: string): PhotoEffectLora => {
	const lora = photoEffectLoras.find((lora) => lora.id === id);
	if (!lora) {
		throw new Error("Photo effect is not found.");
	}
	return lora;
};
