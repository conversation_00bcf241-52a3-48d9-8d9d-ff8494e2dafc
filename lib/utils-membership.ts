import { MembershipID, membershipMapping, MembershipPeriodMonth, MembershipPeriodType, MembershipPeriodYear, MembershipType } from "@/@types/membership-type";

// ======================order======================
// product dev
const ORDER_DEV_PRODUCT_ID_1 = "772883";
const ORDER_DEV_PRODUCT_ID_2 = "772884";
const ORDER_DEV_PRODUCT_ID_3 = "772885";
// product prod
const ORDER_PROD_PRODUCT_ID_1 = "772956";
const ORDER_PROD_PRODUCT_ID_2 = "772957";
const ORDER_PROD_PRODUCT_ID_3 = "772958";

export const ORDER_PRODUCT_ID_1 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_1 : ORDER_DEV_PRODUCT_ID_1;
export const ORDER_PRODUCT_ID_2 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_2 : ORDER_DEV_PRODUCT_ID_2;
export const ORDER_PRODUCT_ID_3 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_3 : ORDER_DEV_PRODUCT_ID_3;

export interface OrderInfo {
	productId: string;
	membership: MembershipType;
	credits: number;
}

const orderVariants: OrderInfo[] = [
	{
		productId: ORDER_PRODUCT_ID_1,
		membership: membershipMapping[MembershipID.Pro],
		credits: 200,
	},
	{
		productId: ORDER_PRODUCT_ID_2,
		membership: membershipMapping[MembershipID.Pro],
		credits: 650,
	},

	{
		productId: ORDER_PRODUCT_ID_3,
		membership: membershipMapping[MembershipID.Pro],
		credits: 1600,
	},
];
export function getOrderProductInfo(productId: string | number): OrderInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return orderVariants.find((v) => v.productId === productId);
}

export enum OrderSource {
	Polar = "polar",
	Cream = "cream",
	Lmsqueezy = "lemonsqueezy",
}

// pending, paid, refunded, partially_refunded
export enum OrderStatus {
	Pending = "pending",
	Paid = "paid",
	Refunded = "refunded",
	PartiallyRefunded = "partially_refunded",
}
export function getOrderStatusName(status: OrderStatus): string {
	switch (status) {
		case OrderStatus.Pending:
			return "Pending";
		case OrderStatus.Paid:
			return "Paid";
		case OrderStatus.Refunded:
			return "Refunded";
		case OrderStatus.PartiallyRefunded:
			return "Partially Refunded";
		default:
			return "Unknown";
	}
}

// ======================Subscription Polar=====================
// incomplete, incomplete_expired, trialing, active, past_due, canceled, unpaid
export enum SubscriptionStatus {
	Incomplete = "incomplete",
	IncompleteExpired = "incomplete_expired",
	Trialing = "trialing",
	Active = "active",
	PastDue = "past_due",
	Canceled = "canceled",
	Unpaid = "unpaid",
}
export function getSubscriptionStatusName(status: SubscriptionStatus): string {
	switch (status) {
		case SubscriptionStatus.Incomplete:
			return "Incomplete";
		case SubscriptionStatus.IncompleteExpired:
			return "Incomplete Expired";
		case SubscriptionStatus.Trialing:
			return "Trialing";
		case SubscriptionStatus.Active:
			return "Active";
		case SubscriptionStatus.PastDue:
			return "Past Due";
		case SubscriptionStatus.Canceled:
			return "Canceled";
		case SubscriptionStatus.Unpaid:
			return "Unpaid";
		default:
			return "Unknown";
	}
}
// product dev
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_STARTER = "7bc08a50-ed7f-4c09-bc8b-b18ace63ed69";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_STARTER = "81681d17-39be-4947-9d72-28ccccf400a2";
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PRO = "9a22b17c-7883-4ff9-9006-d05bf96ea73f";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PRO = "40520b42-ddae-49f7-a52d-71ff15894df1";
const SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PREMIUM = "bcc0eaba-52d7-455c-b422-4d0b94ea75d6";
const SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PREMIUM = "60eba93e-b375-4a1a-b690-e7740dfc15ae";
// product prod
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_STARTER = "744900c3-f8af-48cf-88ec-d23d8c053770";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_STARTER = "5d4a1de8-7357-492f-a337-408cffff6f62";
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PRO = "a5edffb6-76d4-419b-97c0-da6ef262444e";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PRO = "923d42e8-7a87-4cb5-8fa5-662e49fb7dfb";
const SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PREMIUM = "8d9394ed-1065-4679-9f5f-c2a12aa41b09";
const SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PREMIUM = "69d10dfc-93fb-4d17-9830-41100b3e53dc";

export const SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_STARTER : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_STARTER;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_STARTER : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_STARTER;
export const SUBSCRIPTION_PRODUCT_ID_MONTH_PRO =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PRO : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PRO;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_PRO =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PRO : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PRO;
export const SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_MONTH_PREMIUM : SUBSCRIPTION_DEV_PRODUCT_ID_MONTH_PREMIUM;
export const SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_YEAR_PREMIUM : SUBSCRIPTION_DEV_PRODUCT_ID_YEAR_PREMIUM;

export interface MembershipInfo {
	productId: string;
	membership: MembershipType;
	period: MembershipPeriodType;
}

const membershipVariants: MembershipInfo[] = [
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodYear,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_PRO,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_PRO,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodYear,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM,
		membership: membershipMapping[MembershipID.Premium],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM,
		membership: membershipMapping[MembershipID.Premium],
		period: MembershipPeriodYear,
	},
];
export function getMembershipProductInfo(productId: string | number): MembershipInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return membershipVariants.find((v) => v.productId === productId);
}

// 限制用户会员降级，但这不是最好的方案，更好的方案为：**只能在下一个收费周期进行一个会员顶级更改**
export function canChangePlan(userProductId: string | number, targetProductId: string | number): boolean {
	const userMembershipInfo = getMembershipProductInfo(userProductId);
	if (!userMembershipInfo) return true;
	const targetMembershipInfo = getMembershipProductInfo(targetProductId);
	if (!targetMembershipInfo) return true;

	// 如果当前是年订阅
	if (userMembershipInfo.period.value === MembershipPeriodYear.value) {
		// 年订阅不能更改成月订阅
		if (targetMembershipInfo.period.value === MembershipPeriodMonth.value) {
			return false;
		}
		// 年订阅不能降级
		if (targetMembershipInfo.membership.id <= userMembershipInfo.membership.id) {
			return false;
		}
		return true;
	}

	// 如果当前是月订阅
	// 订阅不能降级
	if (targetMembershipInfo.membership.id <= userMembershipInfo.membership.id) {
		return false;
	}
	return true;
}
