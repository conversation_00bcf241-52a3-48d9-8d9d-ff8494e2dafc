import { addDays, format, subMonths, subYears, addMonths } from "date-fns";

export function formatYearMonth(date: Date): string {
	return format(date, "yyyy-MM");
}
export function getPreviousMonth(dateStr: string): Date {
	const date = new Date(dateStr);
	// 减去一个月
	return subMonths(date, 1);
}
export function getPreviousYear(dateStr: string): Date {
	const date = new Date(dateStr);
	// 减去一年
	return subYears(date, 1);
}
export function addDaysToNow(days: number) {
	const now = new Date();
	const futureDate = addDays(now, days);
	return futureDate.getTime();
}
export function addMonthsToNow(months: number) {
	const now = new Date();
	const futureDate = addMonths(now, months);
	return futureDate.getTime();
}

export function getCurrentMonthAndDayString(): {
	yearMonth: string;
	yearMonthDay: string;
} {
	const today = new Date();
	const year = today.getUTCFullYear(); // 获取年份
	const month = (today.getUTCMonth() + 1).toString().padStart(2, "0"); // 获取月份，加1，并确保两位数字
	const day = today.getUTCDate().toString().padStart(2, "0"); // 获取日期，并确保两位数字

	return {
		yearMonth: `${year}${month}`,
		yearMonthDay: `${year}${month}${day}`,
	};
}
