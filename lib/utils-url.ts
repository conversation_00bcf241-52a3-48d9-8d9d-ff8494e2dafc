// URL-safe Base64 编码
export function encodeBase64Url(str: string) {
	const base64 = btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));
	return base64.replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}

// URL-safe Base64 解码
export function decodeBase64Url(str: string) {
	let base64 = str.replace(/-/g, "+").replace(/_/g, "/");
	const paddingLength = (4 - (base64.length % 4)) % 4;
	base64 += "=".repeat(paddingLength);

	return decodeURIComponent(
		atob(base64)
			.split("")
			.map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
			.join(""),
	);
}
