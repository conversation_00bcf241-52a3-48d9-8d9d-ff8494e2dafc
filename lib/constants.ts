// ======================== config keys ========================
// Analytics
export const MIXPANEL_TOKEN = "4ac26940db26d051e5efef6cdddc695f";
export const CLARITY_TOKEN = "r7xe2udj65";
export const GA_TOKEN = "4NX7JJQR9D"; // Google Analytics
export const GTM_TOKEN = "KF9RDN29"; // Google Tag Manager

// ======================== utils keys ========================
export const WEBDOMAIN = "dreampik.art";
export const WEBHOST = process.env.NODE_ENV === "production" ? `https://${WEBDOMAIN}` : "http://localhost:3000";
export const WEBNAME = "Dreampik";
export const EMAIL_CONTACT = `hello@${WEBDOMAIN}`;
export const FEEDBACK_URL = "https://dreampik.userjot.com";
export const CALLBACK_URL_FAL = process.env.NODE_ENV === "production" ? `${WEBHOST}/api/webhook/fal` : "https://dev-next.xav.im/api/webhook/fal";

// Auth
export const ROUTE_PATH_SIGN_IN = "/";
export const ROUTE_PATH_SIGN_IN_AFTER = "/";

// OSS
export const CLOUDFLARE_R2_BUCKET_NAME = "dreampik";
export const OSS_URL_HOST = `https://static.${WEBDOMAIN}/`;

// Cookie
export const COOKIE_BROWSER_USER_ID = "unia"; // 登录后把userId存入cookie，用于多次登录创建新账户，如果多次创建新账号则没有免费额度

// Duration
export const DURATION_1_HOUR = 60 * 60;
export const DURATION_2_HOUR = 2 * DURATION_1_HOUR;
export const DURATION_1_DAY = 24 * DURATION_1_HOUR;
export const DURATION_1_WEEK = 7 * DURATION_1_DAY;
export const DURATION_1_MONTH = 30 * DURATION_1_DAY;
export const DURATION_6_MONTH = 180 * DURATION_1_DAY;

// File Upload limit
export const IMAGE_SIZE_LIMIT_ = 1024 * 1024 * 4; // 4MB
