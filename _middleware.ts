import { NextResponse, type NextRequest } from "next/server";
import { i18nRouter } from "next-i18n-router";
import { i18nConfig } from "./i18n-config";

export function middleware(request: NextRequest) {
	// const { pathname } = request.nextUrl;

	// const isBanIpCountry = await checkCfIpCountry();
	// if (isBanIpCountry) {
	// 	if (pathname === "/signin" || pathname.startsWith("/dashboard")) {
	// 		return NextResponse.redirect(new URL("/forbidden", request.url));
	// 	}
	// }

	return i18nRouter(request, i18nConfig);
}

export const config = {
	matcher: [
		// Skip all internal paths (_next)
		// '/((?!_next).*)',
		// '/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt).*)',
		// Matcher ignoring `/_next/` and `/api/`
		"/((?!api|static|_next|js|images|video|locales|favicon.ico|robots.txt|sitemap.xml|ads.txt|privacy-policy|terms-of-use|forbidden|admin).*)",
		// Optional: only run on root (/) URL
		// '/'
	],
};
