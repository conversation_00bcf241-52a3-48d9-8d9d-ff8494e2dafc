import { Config } from 'next-i18n-router/dist/types';

export const i18nConfig: Config = {
	// locales: [ 'da', 'de', 'en', 'es', 'fr', 'id', 'pt', 'ja', 'ko'],
	locales: [ 'en' ],
	defaultLocale: 'en',
	// localeDetector: false,
	serverSetCookie: 'if-empty',
};
  
export type Locale = (typeof i18nConfig)['locales'][number]

// export const i18nConfig = {
// 	locales: ['en', 'id'],
// 	defaultLocale: 'en'
// };