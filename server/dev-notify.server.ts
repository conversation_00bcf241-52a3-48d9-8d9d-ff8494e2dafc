export async function notifyDev(channel: any, title: any, message: any, output: any) {
	await fetch("https://frostnex-dev-bot.xav.im/dev_notification", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Telegram-Bot-Api-Secret-Token": "Bearer LXGoMmFcfX$j6dS$W7QqoZbzW@r$z6",
		},
		body: JSON.stringify({
			channel: channel,
			title: title,
			message: message,
			output: output,
		}),
	});
}
export function notifyDevEvent(channel: any, title: any, message: any, output: any) {
	if (process.env.NODE_ENV === "production") {
		void notifyDev(channel, title, message, output);
	}
}

export async function notifyPurchase(channel: any, title: any, message: any, output: any) {
	await fetch("https://frostnex-dev-bot.xav.im/purchase", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Telegram-Bot-Api-Secret-Token": "Bearer LXGoMmFcfX$j6dS$W7QqoZbzW@r$z6",
		},
		body: JSON.stringify({
			channel: channel,
			title: title,
			message: message,
			output: output,
		}),
	});
}
export function notifyPurchaseEvent(channel: any, title: any, message: any, output: any) {
	if (process.env.NODE_ENV === "production") {
		void notifyPurchase(channel, title, message, output);
	}
}
