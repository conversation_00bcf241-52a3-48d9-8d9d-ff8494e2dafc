import { getKVKeyBlog, getKVKeyBlogHeads, KV_KEY_BLOG_CATEGORIES } from "@/lib/utils";
import { DURATION_1_MONTH, DURATION_1_WEEK } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { blogCategorySchema, blogItemSchema } from "./db/schema.server";
import { and, eq, desc } from "drizzle-orm";
import { getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { blogDetailSchema, BlogDetailType, BlogPublicHead, blogPublicHeadSchema, BlogStatus } from "@/@types/admin/blog/blog";

const getBlogCategories = async () => {
	let blogCategories: any[] = [];
	const cacheKeyBlogCategories = KV_KEY_BLOG_CATEGORIES;
	const kvDataCategories = (await getValue(cacheKeyBlogCategories)) as any;
	if (kvDataCategories) {
		try {
			blogCategories = kvDataCategories;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog categories data from kv error:", error);
		}
	}
	if (blogCategories.length === 0) {
		const db = getDB();
		blogCategories = await db
			.select({
				id: blogCategorySchema.id,
				slug: blogCategorySchema.slug,
				name: blogCategorySchema.name,
			})
			.from(blogCategorySchema)
			.orderBy(desc(blogCategorySchema.id));
		// console.log(categories);
		if (blogCategories.length > 0) {
			await setValue(cacheKeyBlogCategories, JSON.stringify(blogCategories), DURATION_1_MONTH);
		}
	}
	return blogCategories;
};

export async function getBlogHeadsWithCategory(page: number = 1): Promise<{ blogHeads: BlogPublicHead[]; blogCategories: any[] }> {
	const pageSize = 20;
	const startIndex = (page - 1) * pageSize;

	// 1. Get blogs
	let blogHeads: BlogPublicHead[] = [];
	//先从kv中获取
	const cacheKeyBlogHead = getKVKeyBlogHeads(page);
	const kvDataBlogHead = (await getValue(cacheKeyBlogHead)) as any;
	if (kvDataBlogHead) {
		try {
			const blogHeadsParse = superjson.deserialize(kvDataBlogHead) as BlogPublicHead[];
			// console.log("blogHeadsParse:", blogHeadsParse);
			blogPublicHeadSchema.array().parse(blogHeadsParse);
			blogHeads = blogHeadsParse;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog head data from kv error:", error);
		}
	}

	//再从db中获取

	if (blogHeads.length === 0) {
		const db = getDB();
		blogHeads = await db
			.select({
				categoryId: blogItemSchema.categoryId,
				lang: blogItemSchema.lang,
				slug: blogItemSchema.slug,
				title: blogItemSchema.title,
				image: blogItemSchema.image,
				intro: blogItemSchema.intro,
				publishedAt: blogItemSchema.publishedAt,
			})
			.from(blogItemSchema)
			.where(and(eq(blogItemSchema.lang, "en"), eq(blogItemSchema.status, 1)))
			.orderBy(desc(blogItemSchema.id))
			.offset(startIndex)
			.limit(pageSize);

		if (blogHeads.length > 0) {
			await setValue(cacheKeyBlogHead, superjson.stringify(blogHeads), DURATION_1_WEEK);
		}
	}

	// 2. Get categories
	const blogCategories: any[] = await getBlogCategories();

	return { blogHeads, blogCategories };
}

export async function getBlogWithCategory(lang: string, slug: string): Promise<{ blog: BlogDetailType | null; blogCategories: any[] }> {
	// 1. Get blog
	let blogDetail: BlogDetailType | null = null;
	//先从kv中获取
	let cacheKeyBlog = getKVKeyBlog(lang, slug);
	const kvData = (await getValue(cacheKeyBlog)) as any;
	if (kvData) {
		try {
			const blogDetailParse = superjson.deserialize(kvData) as BlogDetailType;
			// console.log("blogHeadsParse:", blogHeadsParse);
			blogDetailSchema.parse(blogDetailParse);
			blogDetail = blogDetailParse;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog head data from kv error:", error);
		}
	}
	//再从db中获取
	if (!blogDetail) {
		const db = getDB();
		const blogs: BlogDetailType[] = await db
			.select({
				categoryId: blogItemSchema.categoryId,
				lang: blogItemSchema.lang,
				slug: blogItemSchema.slug,
				metaTitle: blogItemSchema.metaTitle,
				metaDescription: blogItemSchema.metaDescription,
				title: blogItemSchema.title,
				image: blogItemSchema.image,
				intro: blogItemSchema.intro,
				html: blogItemSchema.html,
				publishedAt: blogItemSchema.publishedAt,
				status: blogItemSchema.status,
			})
			.from(blogItemSchema)
			.where(and(eq(blogItemSchema.slug, slug), eq(blogItemSchema.lang, lang)));
		if (blogs.length > 0 && blogs[0].status === BlogStatus.Published) {
			blogDetail = blogs[0];
			await setValue(cacheKeyBlog, superjson.stringify(blogs[0]), DURATION_1_MONTH);
		}
	}
	if (!blogDetail) {
		return { blog: null, blogCategories: [] };
	}

	// 2. Get categories
	const blogCategories: any[] = await getBlogCategories();

	return { blog: blogDetail, blogCategories };
}
