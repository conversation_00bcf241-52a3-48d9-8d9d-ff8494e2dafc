import { getKVKeyChangelogHeads } from "@/lib/utils";
import { DURATION_1_WEEK } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { changelogItemSchema } from "./db/schema.server";
import { and, eq, desc } from "drizzle-orm";
import { getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { ChangelogHead, changelogHeadSchema } from "@/@types/admin/changelog/changelog";

export async function getChangelogs(page: number = 1): Promise<{ changelogs: ChangelogHead[] }> {
	const pageSize = 20;
	const startIndex = (page - 1) * pageSize;

	//  Get changelogs from kv
	let changelogs: ChangelogHead[] = [];
	//先从kv中获取
	const cacheKeyChangelogs = getKVKeyChangelogHeads(page);
	const kvDataChangelogs = (await getValue(cacheKeyChangelogs)) as any;
	if (kvDataChangelogs) {
		try {
			const changelogsParse = superjson.deserialize(kvDataChangelogs) as ChangelogHead[];
			// console.log("blogHeadsParse:", blogHeadsParse);
			changelogHeadSchema.array().parse(changelogsParse);
			changelogs = changelogsParse;
		} catch (error) {
			console.log("[getChangelogs] parse changelogs data from kv error:", error);
		}
	}
	//再从db中获取

	if (changelogs.length === 0) {
		const db = getDB();
		changelogs = await db
			.select({
				id: changelogItemSchema.id,
				lang: changelogItemSchema.lang,
				majorVersion: changelogItemSchema.majorVersion,
				minorVersion: changelogItemSchema.minorVersion,
				patchVersion: changelogItemSchema.patchVersion,
				title: changelogItemSchema.title,
				image: changelogItemSchema.image,
				status: changelogItemSchema.status,
				html: changelogItemSchema.html,
				publishedAt: changelogItemSchema.publishedAt,
			})
			.from(changelogItemSchema)
			.where(and(eq(changelogItemSchema.lang, "en"), eq(changelogItemSchema.status, 1)))
			.orderBy(desc(changelogItemSchema.id))
			.offset(startIndex)
			.limit(pageSize);

		if (changelogs.length > 0) {
			await setValue(cacheKeyChangelogs, superjson.stringify(changelogs), DURATION_1_WEEK);
		}
	}

	return { changelogs };
}
