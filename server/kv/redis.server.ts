// import { Redis } from "@upstash/redis";
// import { createClient } from "redis";

// // 本地 Redis 客户端类型
// type LocalRedisClient = ReturnType<typeof createClient>;

// // Redis 客户端接口 - 确保两种客户端使用相同的方法
// interface RedisClientInterface {
// 	connect(): Promise<void>;
// 	disconnect(): Promise<void>;
// 	get(key: string): Promise<string | null>;
// 	set(key: string, value: string, options?: { ex?: number }): Promise<void>;
// 	del(key: string): Promise<void>;
// }

// // 本地 Redis 客户端适配器
// class LocalRedisAdapter implements RedisClientInterface {
// 	private client: LocalRedisClient;

// 	constructor() {
// 		this.client = createClient({
// 			url: process.env.REDIS_URL || "redis://localhost:6379",
// 		});

// 		this.client.on("error", (err: any) => {
// 			console.error("Redis Client Error:", err);
// 		});
// 	}

// 	async connect(): Promise<void> {
// 		await this.client.connect();
// 	}

// 	async disconnect(): Promise<void> {
// 		await this.client.disconnect();
// 	}

// 	async get(key: string): Promise<string | null> {
// 		return await this.client.get(key);
// 	}

// 	async set(key: string, value: string, options?: { ex?: number }): Promise<void> {
// 		if (options?.ex) {
// 			await this.client.set(key, value, { EX: options.ex });
// 		} else {
// 			await this.client.set(key, value);
// 		}
// 	}

// 	async del(key: string): Promise<void> {
// 		await this.client.del(key);
// 	}
// }

// // Upstash Redis 客户端适配器
// class UpstashRedisAdapter implements RedisClientInterface {
// 	private client: Redis;

// 	constructor() {
// 		this.client = new Redis({
// 			url: process.env.UPSTASH_REDIS_REST_URL!,
// 			token: process.env.UPSTASH_REDIS_REST_TOKEN!,
// 		});
// 	}

// 	// Upstash Redis 不需要显式连接
// 	async connect(): Promise<void> {
// 		// 不需要操作
// 	}

// 	// Upstash Redis 不需要显式断开连接
// 	async disconnect(): Promise<void> {
// 		// 不需要操作
// 	}

// 	async get(key: string): Promise<string | null> {
// 		return await this.client.get(key);
// 	}

// 	async set(key: string, value: string, options?: { ex?: number }): Promise<void> {
// 		if (options?.ex) {
// 			await this.client.set(key, value, { ex: options.ex });
// 		} else {
// 			await this.client.set(key, value);
// 		}
// 	}

// 	async del(key: string): Promise<void> {
// 		await this.client.del(key);
// 	}
// }

// // Redis 客户端工厂
// class RedisClientFactory {
// 	static createClient(): RedisClientInterface {
// 		return new UpstashRedisAdapter();
// 		// // 根据环境选择合适的客户端
// 		// if (process.env.NODE_ENV === "production") {
// 		// 	return new UpstashRedisAdapter();
// 		// } else {
// 		// 	return new LocalRedisAdapter();
// 		// }
// 	}
// }

// // 单例模式保持一个全局实例
// let redisClient: RedisClientInterface | null = null;

// // 获取 Redis 客户端
// export async function getRedisClient(): Promise<RedisClientInterface> {
// 	if (!redisClient) {
// 		redisClient = RedisClientFactory.createClient();
// 		await redisClient.connect();
// 	}
// 	return redisClient;
// }

// // 关闭 Redis 连接
// export async function closeRedisConnection(): Promise<void> {
// 	if (redisClient) {
// 		await redisClient.disconnect();
// 		redisClient = null;
// 	}
// }

// // 常用 Redis 操作封装
// export async function getValue(key: string): Promise<string | null> {
// 	const client = await getRedisClient();
// 	return await client.get(key);
// }

// export async function setValue(key: string, value: string, ttlSeconds?: number): Promise<void> {
// 	const client = await getRedisClient();
// 	if (ttlSeconds) {
// 		await client.set(key, value, { ex: ttlSeconds });
// 	} else {
// 		await client.set(key, value);
// 	}
// }

// export async function deleteValue(key: string): Promise<void> {
// 	const client = await getRedisClient();
// 	await client.del(key);
// }
