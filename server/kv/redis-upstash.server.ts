import { Redis } from "@upstash/redis";

export const redisClient = new Redis({
	url: process.env.UPSTASH_REDIS_REST_URL!,
	token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export async function getValue(key: string) {
	return await redisClient.get(key);
}

export async function setValue(key: string, value: string, ttlSeconds?: number) {
	if (ttlSeconds) {
		await redisClient.set(key, value, { ex: ttlSeconds });
	} else {
		await redisClient.set(key, value);
	}
}

export async function deleteValue(key: string) {
	await redisClient.del(key);
}
