import { falGenImages } from "./fal-config.server";

export async function genQwenImageFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
): Promise<string[]> {
	const falAIEndPoint = "fal-ai/qwen-image";
	const payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai qwen-image payload: ", payload);
		console.log("fal.ai qwen-image dev falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 9000, 2000);

	return resultUrls;
}
