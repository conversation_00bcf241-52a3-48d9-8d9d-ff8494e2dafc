import { falGenImages } from "./fal-config.server";

export async function genGoogleImagen4FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	let falAIEndPoint = `fal-ai/${model}`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		num_images: numImages,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai imagen 4 payload: ", payload);
		console.log("fal.ai imagen 4 fal.ai endpoint: ", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 6000, 2000);

	return resultUrls;
}

/** @deprecated */
export async function genGoogleImagen3FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	let falAIEndPoint = `fal-ai/${model}`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		num_images: numImages,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai imagen 3 payload: ", payload);
		console.log("fal.ai imagen 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 6000, 2000);

	return resultUrls;
}
