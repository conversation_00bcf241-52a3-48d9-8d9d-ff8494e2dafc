import { falGenImages } from "./fal-config.server";

export async function genRecraft3FromFal(
	model: string,
	prompt: string,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
	modelStyle?: string | null,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/recraft/v3/text-to-image";
	let payload: any = {
		prompt: prompt,
	};
	if (image) {
		payload.image_url = image;
		payload.strength = 0.5;
		falAIEndPoint = "fal-ai/recraft/v3/image-to-image";
	} else {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	}
	if (modelStyle) {
		payload.style = modelStyle;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai recraft 3 payload: ", payload);
		console.log("fal.ai recraft 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 9000, 1500);

	return resultUrls;
}
