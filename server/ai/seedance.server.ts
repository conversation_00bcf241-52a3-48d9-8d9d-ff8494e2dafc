import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genSeedance1LiteFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio?: string,
	resolution?: string,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = "fal-ai/bytedance/seedance/v1/lite";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		resolution: resolution,
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	} else {
		payload.aspect_ratio = aspectRatio;
		falAIEndPoint += "/text-to-video";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedance 1 lite payload: ", payload);
		console.log("fal.ai Seedance 1 lite falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genSeedance1ProFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio?: string,
	resolution?: string,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = "fal-ai/bytedance/seedance/v1/pro";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		resolution: resolution,
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	} else {
		payload.aspect_ratio = aspectRatio;
		falAIEndPoint += "/text-to-video";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedance 1 pro payload: ", payload);
		console.log("fal.ai Seedance 1 pro falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
