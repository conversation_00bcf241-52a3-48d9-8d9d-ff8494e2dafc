import { falGenImages } from "./fal-config.server";

// https://fal.ai/models/fal-ai/bytedance/dreamina/v3.1/text-to-image
export async function genDreamina3_1FromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
): Promise<string[]> {
	const falAIEndPoint = "fal-ai/bytedance/dreamina/v3.1/text-to-image";
	const payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
		enhance_prompt: true,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai dreamina 3.1 payload: ", payload);
		console.log("fal.ai dreamina 3.1 falRequestModelName:", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 10000, 2000);

	return resultUrls;
}
