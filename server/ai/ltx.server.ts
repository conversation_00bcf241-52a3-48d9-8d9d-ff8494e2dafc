import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genLTXFromFal(model: string, prompt: string, aspectRatio: string, resolution: string, image?: string | null): Promise<string> {
	let falAIEndPoint = "fal-ai/ltxv-13b-098-distilled";
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		resolution: resolution,
		expand_prompt: true,
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai LTX payload: ", payload);
		console.log("fal.ai LTX falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
