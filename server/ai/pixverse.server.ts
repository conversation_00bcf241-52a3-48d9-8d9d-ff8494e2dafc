import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genPixverse4Point5FromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio: string,
	resolution: string,
	modelStyle?: string | null,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = `fal-ai/pixverse/v4.5/${image ? "image-to-video" : "text-to-video"}`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		resolution: resolution,
		duration: duration.toString(),
	};
	if (modelStyle) {
		payload.style = modelStyle;
	}
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Pixverse 4.5 payload: ", payload);
		console.log("fal.ai Pixverse 4.5 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
export async function genPixverse4Point5FastFromFal(
	model: string,
	prompt: string,
	aspectRatio: string,
	resolution: string,
	modelStyle?: string | null,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = `fal-ai/pixverse/v4.5/${image ? "image-to-video" : "text-to-video"}/fast`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		resolution: resolution,
	};
	if (modelStyle) {
		payload.style = modelStyle;
	}
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Pixverse 4.5 Fast payload: ", payload);
		console.log("fal.ai Pixverse 4.5 Fast falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
