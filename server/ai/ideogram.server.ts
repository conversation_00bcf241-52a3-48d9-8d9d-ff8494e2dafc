import { IDEOGRAM_3_BALANCED } from "@/lib/utils-image-model";
import { falGenImages } from "./fal-config.server";

export async function genIdeogramFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
	modelStyle?: string | null,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/ideogram/v3";
	let renderingSpeed = "TURBO";
	if (model === IDEOGRAM_3_BALANCED.model) {
		renderingSpeed = "BALANCED";
	}
	let payload: any = {
		prompt: prompt,
		rendering_speed: renderingSpeed,
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
	};
	if (image) {
		payload.image_urls = [image];
	}
	if (modelStyle) {
		payload.style = modelStyle;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai ideogram 3 payload: ", payload);
		console.log("fal.ai ideogram 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload, 10000, 2000);

	return resultUrls;
}
