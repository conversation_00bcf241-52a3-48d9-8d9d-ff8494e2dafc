import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genHailuo2StandardFromFal(model: string, prompt: string, duration: number, image?: string | null): Promise<string> {
	let falAIEndPoint = `fal-ai/minimax/hailuo-02/standard/${image ? "image-to-video" : "text-to-video"}`;
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
	};
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Hailuo 2 Standard payload: ", payload);
		console.log("fal.ai Hailuo 2 Standard falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genHailuo2ProFromFal(model: string, prompt: string, image?: string | null): Promise<string> {
	let falAIEndPoint = `fal-ai/minimax/hailuo-02/pro/${image ? "image-to-video" : "text-to-video"}`;
	let payload: any = {
		prompt: prompt,
	};
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Hailuo 2 Pro payload: ", payload);
		console.log("fal.ai Hailuo 2 Pro falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
