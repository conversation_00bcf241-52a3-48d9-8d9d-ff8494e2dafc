import { GOOGLE_VEO_3 } from "@/lib/utils-video-model";
import { falGenVideoWithWebhook } from "./fal-config.server";

export async function genVeoFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio?: string,
	resolution?: string,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = "fal-ai/veo3/fast";
	if (model === GOOGLE_VEO_3.model) {
		falAIEndPoint = "fal-ai/veo3";
	}
	let payload: any = {
		prompt: prompt,
		duration: `${duration.toString()}s`,
	};
	if (image) {
		falAIEndPoint += "/image-to-video";
		payload.image_url = image;
	} else {
		payload.aspect_ratio = aspectRatio;
		if (resolution) {
			payload.resolution = resolution;
		}
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Veo 3 payload: ", payload);
		console.log("fal.ai Veo 3 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
