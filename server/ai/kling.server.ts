import { falGenVideoWithWebhook } from "./fal-config.server";

/** @deprecated */
export async function genKling1Point6FromFal(model: string, prompt: string, duration: number, aspectRatio: string): Promise<string> {
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai kling 1.6 payload: ", payload);
	}

	const request_id = await falGenVideoWithWebhook("fal-ai/kling-video/v1.6/standard/text-to-video", payload);

	return request_id;
}

export async function genKling2Point1MasterFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio?: string,
	image?: string | null,
): Promise<string> {
	let falAIEndPoint = "fal-ai/kling-video/v2.1/master";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	} else {
		payload.aspect_ratio = aspectRatio;
		falAIEndPoint += "/text-to-video";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Master payload: ", payload);
		console.log("fal.ai Kling 2.1 Master falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
export async function genKling2Point1ProFromFal(model: string, prompt: string, duration: number, aspectRatio?: string, image?: string | null): Promise<string> {
	if (!image) {
		throw new Error("Kling 2.1 Pro currently only supports image to video.");
	}
	let falAIEndPoint = "fal-ai/kling-video/v2.1/pro/image-to-video";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		// aspect_ratio: aspectRatio,
		image_url: image,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Pro payload: ", payload);
		console.log("fal.ai Kling 2.1 Pro falAIEndPoint:", falAIEndPoint);
	}
	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
export async function genKling2Point1StandardFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio?: string,
	image?: string | null,
): Promise<string> {
	if (!image) {
		throw new Error("Kling 2.1 Standard currently only supports image to video.");
	}
	let falAIEndPoint = "fal-ai/kling-video/v2.1/standard/image-to-video";

	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		// aspect_ratio: aspectRatio,
		image_url: image,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Standard payload: ", payload);
		console.log("fal.ai Kling 2.1 Standard falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genKling2FromFal(model: string, prompt: string, duration: number, aspectRatio?: string, image?: string | null): Promise<string> {
	let falAIEndPoint = "fal-ai/kling-video/v2/master";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
	};
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/image-to-video";
	} else {
		payload.aspect_ratio = aspectRatio;
		falAIEndPoint += "/text-to-video";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2 Master payload: ", payload);
		console.log("fal.ai Kling 2 Master falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenVideoWithWebhook(falAIEndPoint, payload);
	return request_id;
}
