import { headers } from "next/headers";

const banIpCountry = [
	"T1", // Tor network
	"AF", // Afghanistan 阿富汗
	"BD", // Bangladesh 孟加拉国
	"KH", // Cambodia 柬埔寨
	"CD", // Congo 民主刚果
	"CG", // Congo 刚果
	"GH", // Ghana 加纳
	"GT", // Guatemala 危地马拉
	"ID", // Indonesia 印度尼西亚
	"IN", // India 印度
	"IR", // Iran 伊朗
	"IQ", // Iraq 伊拉克
	"LA", // Laos" 老挝
	"LY", // Libya 利比亚
	"MM", // Myanmar 缅甸
	"NP", // Nepal 尼泊尔
	"NE", // Niger 尼日尔
	"NG", // Nigeria 尼日利亚
	"PK", // Pakistan 巴基斯坦
	"PY", // Paraguay 巴拉圭
	"PH", // Philippines 菲律宾
	"PS", // Palestine 巴勒斯坦
	"RU", // Russia 俄罗斯
	"SD", // Sudan 苏丹
	"SS", // South Sudan 南苏丹
	"LK", // Sri Lanka 斯里兰卡
	"SY", // Syria 叙利亚
	"TZ", // Tanzania 坦桑尼亚
	// "TH", // Thailand 泰国
	"VN", // Vietnam 越南
	"YE", // Yemen 也门
];

export async function getCfIpAndCountry(): Promise<{ countryCode: string | null; ip: string | null }> {
	const headersList = await headers();
	const countryCode = headersList.get("cf-ipcountry");
	const ip = headersList.get("cf-connecting-ip");
	return { countryCode, ip };
}

export async function checkCfIpCountry(cfIpCountryCode?: string | null): Promise<boolean | null> {
	let countryCode = cfIpCountryCode;
	if (!countryCode) {
		countryCode = (await getCfIpAndCountry()).countryCode;
	}
	if (countryCode) {
		return banIpCountry.some((country) => country.toUpperCase() === countryCode.toUpperCase());
	}
	return null;
}
